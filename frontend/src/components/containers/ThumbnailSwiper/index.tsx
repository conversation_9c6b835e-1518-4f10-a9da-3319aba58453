'use client';
import React, { useEffect, useMemo, useRef } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import moment from 'moment';
import SwiperCore from 'swiper';
import { Autoplay, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import DiscountBadge from '@/components/atoms/DiscountBadge';
import ItemIcon from '@/components/atoms/itemIcon';
import ShareIcon from '@/components/atoms/shareIcon';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { PlayerType, usePlayerStore } from '@/store/usePlayer';
import { generateShopItemUrl } from '@/consts/url';
import { getIsNowOnSale } from '@/utils/item';
import { isDigitalBundle } from '@/utils/itemTypes';
import { navigateShare } from '@/utils/share';
import type { GachaItemFile } from '@/types/gacha';
import { Discount, ITEM_TYPE, ItemFiles, SingleItem } from '@/types/shopItem';
import 'swiper/css';
import 'swiper/css/pagination';

interface ThumbnailSwiperProps {
  props: {
    shopName: string;
    creatorName: string;
    itemFiles?: ItemFiles | GachaItemFile[];
    samples?: SingleItem[] | GachaItemFile[];
    title?: string;
    priceSet?: number;
    currentPrice?: number;
    thumbnailRatio?: number;
    thumbnail?: string;
    discount?: Discount;
    isPreview?: boolean;
    itemId: string;
    itemType: number;
    isSingleSale?: boolean;
    identityId?: string;
  };
}

interface PriceRowProps {
  label: string;
  price: number;
  hasDiscount?: boolean;
  discountPercentage?: number;
  showDiscountBadge?: boolean;
  isPreview?: boolean;
  hasTilde?: boolean;
}

const PriceRow: React.FC<PriceRowProps> = ({
  label,
  price,
  hasDiscount,
  discountPercentage,
  showDiscountBadge,
  isPreview,
  hasTilde,
}) => {
  return (
    <div className="flex h-7 items-center gap-1 text-bold-22">
      <div className="flex items-center">
        {label !== '' && <span className="pr-1 text-medium-13">{label}</span>}
        <div>
          ¥{price.toLocaleString()}
          {hasTilde && '〜'}
        </div>
        <span className="text-regular-11">（税込）</span>
      </div>
      {hasDiscount && discountPercentage && (showDiscountBadge || isPreview) && (
        <DiscountBadge percentage={discountPercentage} />
      )}
    </div>
  );
};

const ThumbnailSwiper: React.FC<ThumbnailSwiperProps> = ({ props }) => {
  const {
    shopName,
    creatorName,
    itemId,
    itemType,
    identityId,
    itemFiles: propFiles,
    samples: propSamples,
    title: propTitle,
    priceSet: propPriceSet,
    currentPrice: propCurrentPrice,
    thumbnailRatio: propThumbnailRatio,
    thumbnail: propThumbnail,
    discount: propDiscount,
    isPreview = false,
    isSingleSale = false,
  } = props;
  const playerStore = usePlayerStore();
  const { setPlayerProps, onPlayerOpen, isPlayerOpen } = playerStore;
  const exhibitsStore = useExhibitsStore();

  const exhibitItem = useMemo(
    () => exhibitsStore.exhibits.find((e) => e.itemId === itemId),
    [exhibitsStore.exhibits, itemId],
  );
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const itemFiles = propFiles ?? (isDigitalBundle(exhibitItem) ? exhibitItem?.itemFiles : []) ?? [];
  const samples = propSamples || exhibitItem?.samples || [];
  const title = propTitle || exhibitItem?.title;
  const priceSet = propPriceSet || exhibitItem?.priceSet;
  const currentPrice = propCurrentPrice || exhibitItem?.priceSet;
  const thumbnailRatio = propThumbnailRatio || exhibitItem?.thumbnailRatio;
  const thumbnail = propThumbnail || exhibitItem?.thumbnail;
  const discount = propDiscount || exhibitItem?.discount;
  const discountPercentage = discount?.percentage && discount?.percentage;

  const swiperRef = useRef<SwiperCore | null>(null);

  const thumbnailList = useMemo(() => {
    const _thumbnailList = [...samples]
      .filter((item) => item.thumbnail)
      .map((item) => ({
        thumbnail: item.thumbnail,
        thumbnailRatio: item.thumbnailRatio,
        title: item.title,
        id: item.id,
        type: item.type,
      }));
    return [{ thumbnail, thumbnailRatio, title }, ..._thumbnailList];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemFiles, samples, thumbnail]);

  const lowestPrice = useMemo(() => {
    if (isSingleSale) {
      return Math.min(
        ...(itemFiles.map((file) => (file as SingleItem).currentPrice || currentPrice || priceSet) as number[]),
      );
    } else {
      return currentPrice || priceSet || 100;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemFiles, priceSet, currentPrice, isSingleSale]);

  const isShowSaleInformation = useMemo(() => {
    return getIsNowOnSale({
      startAt: discount?.start,
      endAt: discount?.end,
      discountRate: discount?.percentage,
    });
  }, [discount]);

  const handleClick = (id?: string) => {
    if (!id) return;
    const item = samples.find((item) => item.id === id);
    if (!item) return;
    if (item.type === 'image') return;
    onPlayerOpen();
    setPlayerProps({
      src: item.src!,
      thumbnail: item.thumbnail!,
      type: item.type as PlayerType,
    });
    if (swiperRef.current) {
      swiperRef.current.autoplay.stop();
    }
  };

  useEffect(() => {
    if (!isPlayerOpen) {
      swiperRef.current?.autoplay.start();
    }
  }, [isPlayerOpen]);

  const handleShare = async () => {
    sendGTMEvent({ event: 'fanme_item_detail_click', item_id: itemId });
    const url = generateShopItemUrl(identityId, itemId);
    const text = `${title}\n${creatorName} - ${shopName}\n\n👇👇購入はこちらから！\n${url}\n\n#FANME #ファンミー #FANMEshop\n@FANME__officialより`;
    await navigateShare({ text });
  };

  return (
    <div className="flex flex-col gap-2 rounded-b-2xl bg-white px-4 pb-4 pt-3">
      <Swiper
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        rewind={true}
        pagination={{
          clickable: true,
        }}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        modules={[Pagination, Autoplay]}
        className="thumbnailSwiper h-96 w-88 !overflow-y-auto !overflow-x-hidden"
      >
        {thumbnailList.map((item) => (
          <SwiperSlide key={item.title}>
            <div className="relative" onClick={() => handleClick('id' in item ? item.id : undefined)}>
              <ItemIcon
                thumbnail={item.thumbnail || ''}
                thumbnailRatio={item.thumbnailRatio || 1}
                title={item.title || ''}
                size={352}
              />
              {'type' in item && item.type !== 'image' && (
                <div className="absolute inset-0 grid place-content-center">
                  <OutlinedButton
                    buttonColor="gray"
                    buttonShape="circle"
                    buttonType="play"
                    buttonSize="md"
                    className="absolute inset-0 m-auto"
                  />
                </div>
              )}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      <h1 className="break-all text-bold-17">{title}</h1>
      <div>
        <div>
          <div className="flex items-center justify-between gap-2">
            <div>
              {isSingleSale && itemFiles.length > 0 && (
                <PriceRow
                  label="単品"
                  price={lowestPrice}
                  hasDiscount={!!discount?.percentage}
                  discountPercentage={discountPercentage}
                  showDiscountBadge={isShowSaleInformation}
                  isPreview={isPreview}
                  hasTilde
                />
              )}
              <PriceRow
                label={itemType !== ITEM_TYPE.digitalBundle ? '' : 'セット'}
                price={currentPrice || priceSet || 0}
                hasDiscount={!!discount?.percentage}
                discountPercentage={discountPercentage}
                showDiscountBadge={isShowSaleInformation}
                isPreview={isPreview}
              />
            </div>
            <button className="p-2" onClick={handleShare} disabled={!shopName}>
              <ShareIcon />
            </button>
          </div>
          {discount?.percentage && discount?.percentage > 0 && (isShowSaleInformation || isPreview) ? (
            <div className="mt-2 flex items-center gap-1 text-medium-13 text-green-300">
              {(discount.start || discount.end) && (
                <ShopPublicImage src="/images/icons/Sale_green.svg" alt="sale" width={20} height={20} />
              )}
              {discount.start ? `${moment(discount.start).format('YYYY年MM月DD日 HH:mm') + 'から'}` : ''}&nbsp;
              {discount.end ? `${moment(discount.end).format('YYYY年MM月DD日 HH:mm')}まで` : ''}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ThumbnailSwiper;
