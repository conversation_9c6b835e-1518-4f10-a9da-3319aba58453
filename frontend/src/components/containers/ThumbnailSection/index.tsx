'use client';
import React, { useEffect, useMemo, useRef } from 'react';
import clsx from 'clsx';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import { OverlayImage } from '@/components/atoms/itemIcon';
import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';
import SectionInstruction from '@/components/atoms/sectionInstruction';
import Tab from '@/components/atoms/tab';
import Tabs from '@/components/atoms/tab/tabs';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { MAX_SINGLE_IMAGE_SIZE_MB } from '@/consts/sizes';
import { useHandleSetCustomThumbnail } from '@/hooks/useHandleSetCustomThumbnail';
import { isDigitalBundle } from '@/utils/itemTypes';
import { generateProcessedThumbnail } from '@/utils/thumbnail';

type ThumbnailSectionProps = {
  numbering: string;
};

const ThumbnailSection = ({ numbering }: ThumbnailSectionProps) => {
  const {
    exhibits,
    setThumbnail,
    setItemFiles,
    setThumbnailType,
    setThumbnailCustomImage,
    setSelectedIndex,
    setThumbnailBlurLevel,
    setThumbnailWatermarkLevel,
  } = useExhibitsStore();

  const { handleSetCustomThumbnail } = useHandleSetCustomThumbnail();
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const isDigitalBundleItem = isDigitalBundle(exhibitItem);
  const {
    title,
    thumbnailType = 'custom',
    thumbnailCustomImage = '',
    thumbnailBlurLevel = THUMBNAIL_BLUR_LEVEL.NONE,
    thumbnailWatermarkLevel = THUMBNAIL_WATERMARK_LEVEL.WHITE,
  } = exhibitItem ?? {};
  const selectedIndex = isDigitalBundleItem ? (exhibitItem?.selectedIndex ?? null) : null;
  const itemFiles = isDigitalBundleItem ? (exhibitItem?.itemFiles ?? []) : [];

  const customThumbnailRef = useRef<HTMLInputElement>(null);

  const handleBlurChange = async (value: number) => {
    const selectedFile = itemFiles.find((file) => file.selected);
    if (!selectedFile) return;

    const resultFile = await generateProcessedThumbnail(
      selectedFile,
      value,
      selectedFile.watermark ?? thumbnailWatermarkLevel,
    );

    if (resultFile?.processedThumbnail) {
      updateFileSettings(selectedFile.id, {
        processedThumbnail: resultFile.processedThumbnail,
        blur: value,
        watermark: selectedFile.watermark ?? thumbnailWatermarkLevel,
      });

      setThumbnail(itemId, resultFile.processedThumbnail);
      setThumbnailBlurLevel(itemId, value);
    }
  };

  const handleWatermarkChange = async (value: number) => {
    const selectedFile = itemFiles.find((file) => file.selected);
    if (!selectedFile) return;

    const resultFile = await generateProcessedThumbnail(selectedFile, selectedFile.blur ?? thumbnailBlurLevel, value);

    if (resultFile?.processedThumbnail) {
      updateFileSettings(selectedFile.id, {
        processedThumbnail: resultFile.processedThumbnail,
        blur: selectedFile.blur ?? thumbnailBlurLevel,
        watermark: value,
      });

      setThumbnail(itemId, resultFile.processedThumbnail);
      setThumbnailWatermarkLevel(itemId, value);
    }
  };

  const handleChange = async (value: 'custom' | 'upload') => {
    setThumbnailType(itemId, value);
  };

  // Handle thumbnail type and image changes

  useEffect(() => {
    if (thumbnailType === 'custom') {
      setThumbnail(itemId, thumbnailCustomImage);
    } else if (thumbnailType === 'upload') {
      if (selectedIndex !== null) {
        handleSelectThumbnail(selectedIndex);
        if (itemFiles.length > 0) {
          itemFiles.forEach((file) => {
            if (file.selected) {
              setThumbnail(itemId, file.processedThumbnail || file.src);
            }
          });
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [thumbnailType, thumbnailCustomImage, selectedIndex]);

  const updateFileSettings = (
    fileId: string,
    settings: {
      processedThumbnail: string;
      blur: number;
      watermark: number;
    },
  ) => {
    setItemFiles(
      itemId,
      itemFiles.map((file) => (file.id === fileId ? { ...file, ...settings } : file)),
    );
  };

  const handleSelectThumbnail = (index: number) => {
    if (index === null) return;

    const updatedFiles = itemFiles.map((file, i) => ({
      ...file,
      selected: i === index,
    }));

    const selectedFile = updatedFiles[index];

    setSelectedIndex(itemId, index);
    setItemFiles(itemId, updatedFiles);
    setThumbnail(itemId, selectedFile.processedThumbnail || selectedFile.src);
    setThumbnailBlurLevel(itemId, selectedFile.blur ?? THUMBNAIL_BLUR_LEVEL.NONE);
    setThumbnailWatermarkLevel(itemId, selectedFile.watermark ?? THUMBNAIL_WATERMARK_LEVEL.WHITE);
  };

  const handleOnUploadCustomThumbnail = () => {
    handleChange('custom');
    customThumbnailRef.current?.click();
  };

  useEffect(() => {
    if (thumbnailType === 'custom') {
      setThumbnail(itemId, thumbnailCustomImage);
    } else if (thumbnailType === 'upload' && selectedIndex !== null) {
      const selectedFile = itemFiles[selectedIndex];
      if (selectedFile) {
        setThumbnail(itemId, selectedFile.processedThumbnail || selectedFile.src);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [thumbnailType, thumbnailCustomImage, selectedIndex, itemFiles]);

  const CustomThumbnailRadioLabel = (
    <div className="flex flex-col gap-2 pl-2">
      <div className="text-medium-16 text-secondary">サムネイル画像のアップロード</div>
      <span className="text-regular-11 text-secondary">専用の画像をアップロードします</span>
    </div>
  );

  const UploadThumbnailRadioLabel = (
    <div className="flex flex-col gap-2 pl-2">
      <div className="text-medium-16 text-secondary">商品データから選ぶ</div>
      <span className="text-regular-11 text-secondary">【01】でアップロードした商品データから選びます。</span>
    </div>
  );

  const blurSetting = [
    {
      label: 'なし',
      src: '/shop/images/icons/setting/BlurNone.svg',
    },
    {
      label: '弱い',
      src: '/shop/images/icons/setting/Blur1.svg',
    },
    {
      label: '強い',
      src: '/shop/images/icons/setting/Blur2.svg',
    },
  ];

  const watermarkSetting = [
    {
      label: 'なし',
      src: '/shop/images/icons/setting/WatermarkNone.svg',
    },
    {
      label: '白文字',
      src: '/shop/images/icons/setting/WatermarkWhite.svg',
    },
    {
      label: '黒文字',
      src: '/shop/images/icons/setting/WatermarkBlack.svg',
    },
  ];

  const BlurSettingLabels = blurSetting.map(({ label, src }, index) => (
    <div key={index} className="w-full">
      <div className="flex items-center">
        <img src={src} className="ml-1 size-8.5" />
        <div className="w-full">{label}</div>
      </div>
    </div>
  ));

  const WatermarkSettingLabels = watermarkSetting.map(({ label, src }, index) => (
    <div key={index} className="w-full">
      <div className="flex items-center">
        <img src={src} className="ml-1 size-8.5" />
        <div className="w-full">{label}</div>
      </div>
    </div>
  ));

  const popupHeader = 'サムネイルとは看板画像のこと';
  const popupContent = (
    <div className="flex items-center justify-center bg-navy-50 py-4">
      <ShopPublicImage src={'/images/thumbnailCover.webp'} width={242} height={86} alt="modal" />
    </div>
  );
  const popupFooter =
    'サムネイルは、ショップのトップとサンプルデータの一番最初に表示される画像で、見栄えがいい画像ほど閲覧率が上がります。音源データを販売する際は、できるかぎりこだわりの画像を設定しましょう。';
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber title="商品表紙(サムネイル)設定" numbering={numbering} required className="!mb-0" />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>
        <SectionInstruction title="2つの方法から選択できます" className="mb-4" />
        <RadioGroup name="thumbnailSelect" defaultValue={thumbnailType} className="items-start" direction="column">
          <>
            <div className="w-full rounded-xl border-none px-4 pb-3 pt-4 shadow-section-shadow">
              <RadioGroupItem
                label={CustomThumbnailRadioLabel}
                value="custom"
                name="thumbnailSelect"
                selectedValue={thumbnailType}
                onChange={handleChange.bind(null, 'custom')}
              />
              <div className="mt-4 flex w-full items-center justify-center">
                {thumbnailCustomImage ? (
                  <div className="relative flex size-40 items-center justify-center overflow-hidden rounded-lg">
                    <OutlinedButton
                      buttonColor="transparent"
                      buttonShape="circle"
                      buttonType="edit"
                      buttonSize="md"
                      className="absolute inset-0 z-10 m-auto"
                      onClick={handleOnUploadCustomThumbnail}
                    />
                    <OverlayImage src={`${thumbnailCustomImage}?t=${new Date().getTime()}`} />
                    <Image
                      src={`${thumbnailCustomImage}?t=${new Date().getTime()}`}
                      alt="thumbnail"
                      width={160}
                      height={160}
                      className="absolute size-40 object-contain"
                    />
                  </div>
                ) : (
                  <div
                    className="relative flex size-40 items-center justify-center overflow-hidden rounded-lg bg-[url('/shop/images/empty.svg')] bg-repeat"
                    onClick={handleOnUploadCustomThumbnail}
                  >
                    <Button buttonType="light-small" buttonShape="circle" buttonSize="sm">
                      <ShopPublicImage src="/images/icons/Plus.svg" width={17} height={17} alt="upload file" />
                    </Button>
                  </div>
                )}
                <input
                  type="file"
                  name="thumb"
                  accept="image/jpeg, image/png, image/gif, image/jpg"
                  ref={customThumbnailRef}
                  hidden
                  onChange={async (e: React.ChangeEvent<HTMLInputElement>) => {
                    await handleSetCustomThumbnail(
                      {
                        itemId,
                        title,
                        setThumbnailCustomImage,
                      },
                      e,
                    );

                    e.target.value = ''; // 同じファイルを選択してもonChangeが発火させるようクリア
                  }}
                />
              </div>
              <ul>
                <li className="mt-3 flex items-center gap-1 text-regular-11 text-gray-500">
                  <ShopPublicImage src="/images/icons/PhotoIcn.svg" width={16} height={16} alt="custom thumbnail" />
                  <span className="text-regular-13 text-secondary"> 画像</span>
                  .jpg / .png / .gif [最大容量] {MAX_SINGLE_IMAGE_SIZE_MB}MB
                </li>
              </ul>
            </div>
            <div className="w-full rounded-xl border-none px-4 pb-7 pt-4 shadow-section-shadow">
              <RadioGroupItem
                label={UploadThumbnailRadioLabel}
                value="upload"
                name="thumbnailSelect"
                selectedValue={thumbnailType}
                onChange={handleChange.bind(null, 'upload')}
                disabled={itemFiles.length === 0 || itemFiles.every((file) => file.type === 'audio')}
              />
              {thumbnailType === 'upload' && (
                <>
                  <p className="my-6 text-regular-13">サムネイルにしたい画像を選択してください</p>
                  <div className="grid grid-cols-3 gap-4">
                    {itemFiles.length > 0 &&
                      itemFiles.map((file, index) => {
                        if (file.type !== 'audio' && file.processedThumbnail)
                          return (
                            <div className="flex justify-center" key={index}>
                              <div
                                className={clsx(
                                  'relative box-content flex aspect-square size-26 min-w-26 cursor-pointer items-center justify-center overflow-hidden rounded-lg border-3',
                                  file.selected ? 'border-green-100' : 'border-transparent',
                                )}
                                onClick={() => handleSelectThumbnail(index)}
                              >
                                <div
                                  className={clsx(
                                    'absolute bottom-2 right-2 z-10 grid size-7 place-content-center rounded-full border-2 border-white bg-green-100',
                                    { hidden: !file.selected },
                                  )}
                                >
                                  <ShopPublicImage
                                    src="/images/icons/CheckWhite.svg"
                                    width={16}
                                    height={12.8}
                                    alt="check"
                                    className="aspect-square"
                                  />
                                </div>

                                <Image
                                  src={file.processedThumbnail!}
                                  width={104}
                                  height={104}
                                  alt={file?.title || ''}
                                  className="relative h-full w-auto object-contain"
                                />
                              </div>
                            </div>
                          );
                      })}
                  </div>
                  <div className="maskIconTitle relative my-4 pl-8 text-medium-16 text-secondary">透かし設定</div>
                  <Tabs
                    value={itemFiles.find((file) => file.selected)?.watermark ?? THUMBNAIL_WATERMARK_LEVEL.WHITE}
                    onChange={handleWatermarkChange}
                    type="effect"
                  >
                    {WatermarkSettingLabels.map((label, index) => (
                      <Tab key={index}>{label}</Tab>
                    ))}
                  </Tabs>
                  <div className="blurIconTitle relative mb-4 mt-6 pl-8 text-medium-16 text-secondary">ぼかし設定</div>
                  <Tabs
                    value={itemFiles.find((file) => file.selected)?.blur ?? THUMBNAIL_BLUR_LEVEL.NONE}
                    onChange={handleBlurChange}
                    type="effect"
                  >
                    {BlurSettingLabels.map((label, index) => (
                      <Tab key={index}>{label}</Tab>
                    ))}
                  </Tabs>
                </>
              )}
            </div>
          </>
        </RadioGroup>
      </div>
    </section>
  );
};

export default ThumbnailSection;
