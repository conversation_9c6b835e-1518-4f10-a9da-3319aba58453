'use client';
import React, { useEffect, useMemo, useRef } from 'react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import { OverlayImage } from '@/components/atoms/itemIcon';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { useHandleSetCustomThumbnail } from '@/hooks/useHandleSetCustomThumbnail';

type ThumbnailSectionProps = {
  numbering: string;
};

const ThumbnailSectionCustomOnly = ({ numbering }: ThumbnailSectionProps) => {
  const { exhibits, setThumbnail, setThumbnailCustomImage, setThumbnailType } = useExhibitsStore();

  const { handleSetCustomThumbnail } = useHandleSetCustomThumbnail();
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { title, thumbnailCustomImage = '' } = exhibitItem ?? {};

  // サムネイルタイプを固定
  useEffect(() => {
    if (itemId) {
      setThumbnailType(itemId, 'custom');
    }
  }, [itemId, setThumbnailType]);

  // サムネイルを設定
  useEffect(() => {
    if (itemId && thumbnailCustomImage) {
      setThumbnail(itemId, thumbnailCustomImage);
    }
  }, [itemId, thumbnailCustomImage, setThumbnail]);

  const customThumbnailRef = useRef<HTMLInputElement>(null);

  const popupHeader = 'サムネイルとは看板画像のこと';
  const popupContent = (
    <div className="flex items-center justify-center bg-navy-50 py-4">
      <ShopPublicImage src={'/images/thumbnailCover.webp'} width={242} height={86} alt="modal" />
    </div>
  );
  const popupFooter =
    'サムネイルは、ショップのトップとサンプルデータの一番最初に表示される画像で、見栄えがいい画像ほど閲覧率が上がります。音源データを販売する際は、できるかぎりこだわりの画像を設定しましょう。';
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  const handleOnUploadCustomThumbnail = () => {
    customThumbnailRef.current?.click();
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber title="商品表紙(サムネイル)設定" numbering={numbering} required className="!mb-0" />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>
        <div className="mt-4 flex w-full items-center justify-center">
          {thumbnailCustomImage ? (
            <div className="relative flex size-40 items-center justify-center overflow-hidden rounded-lg">
              <OutlinedButton
                buttonColor="transparent"
                buttonShape="circle"
                buttonType="edit"
                buttonSize="md"
                className="absolute inset-0 z-10 m-auto"
                onClick={handleOnUploadCustomThumbnail}
              />
              <OverlayImage src={`${thumbnailCustomImage}?t=${new Date().getTime()}`} />
              <Image
                src={`${thumbnailCustomImage}?t=${new Date().getTime()}`}
                alt="thumbnail"
                width={160}
                height={160}
                className="absolute size-40 object-contain"
              />
            </div>
          ) : (
            <div
              className="relative flex size-40 items-center justify-center overflow-hidden rounded-lg bg-[url('/shop/images/empty.svg')] bg-repeat"
              onClick={handleOnUploadCustomThumbnail}
            >
              <Button buttonType="light-small" buttonShape="circle" buttonSize="sm">
                <ShopPublicImage src="/images/icons/Plus.svg" width={17} height={17} alt="upload file" />
              </Button>
            </div>
          )}
          <input
            type="file"
            name="thumb"
            accept="image/jpeg, image/png, image/gif, image/jpg"
            ref={customThumbnailRef}
            hidden
            onChange={async (e: React.ChangeEvent<HTMLInputElement>) => {
              await handleSetCustomThumbnail(
                {
                  itemId,
                  title,
                  setThumbnailCustomImage,
                },
                e,
              );
              e.target.value = ''; // 同じファイルを選択してもonChangeが発火させるようクリア
            }}
          />
        </div>
      </div>
    </section>
  );
};

export default ThumbnailSectionCustomOnly;
