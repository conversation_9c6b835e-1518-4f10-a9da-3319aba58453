'use client';
import React, { useRef } from 'react';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { ACCEPTED_FILE_TYPES } from '@/consts/file';
import { getUserIdentityId } from '@/utils/base';
import { handleFileUpload } from '@/utils/upload';
import { ItemFiles } from '@/types/shopItem';

interface IUploadItemProps {
  itemFiles?: ItemFiles;
  setFiles: (files: ItemFiles) => void;
  isPublic?: boolean;
  onProgress?: (id: string, progress: number) => void;
  shopLimitation: ShopLimitation;
}
const UploadFile = ({ itemFiles, setFiles, isPublic, onProgress, shopLimitation }: IUploadItemProps) => {
  const params = useParams();
  const identityId = getUserIdentityId(params.identityId as string);
  const uploadFile = useRef<HTMLInputElement>(null);
  const handleUploadItem = () => {
    uploadFile.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    // Use the new utility function
    await handleFileUpload({
      currentFiles: event.target.files,
      existingFiles: itemFiles || [],
      shopLimitation,
      identityId,
      setFiles,
      onProgress,
      isPublic,
      isGacha: false,
    });
  };

  return (
    <div className="grid">
      <div className="transparent-bg flex size-26 items-center justify-center rounded-lg" onClick={handleUploadItem}>
        <Button buttonType="light-small" buttonShape="circle" buttonSize="sm">
          <ShopPublicImage src="/images/icons/Plus.svg" width={17} height={17} alt="upload file" />
        </Button>
        <input
          type="file"
          name="thumb"
          accept={ACCEPTED_FILE_TYPES}
          onChange={(e) => {
            handleFileChange(e);
            e.target.value = ''; // 同じファイルを選択してもonChangeが発火させるようクリア
          }}
          ref={uploadFile}
          hidden
        />
      </div>
    </div>
  );
};

export default UploadFile;
