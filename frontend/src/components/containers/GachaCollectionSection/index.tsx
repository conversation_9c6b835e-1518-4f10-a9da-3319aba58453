'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import GachaCollection from '@/components/containers/GachaCollection';
import GachaReviewModal from '@/components/containers/GachaReviewModal';
import { useGachaItemStore } from '@/store/useGachaItem';
import { GachaItemFile } from '@/types/gacha';

type GachaCollectionSectionProps = {
  isPreview?: boolean;
  itemFiles?: GachaItemFile[];
  hasBg?: boolean;
};

const GachaCollectionSection = (props: GachaCollectionSectionProps) => {
  const { isPreview = false } = props;
  const params = useParams();
  const itemId = params.id as string;
  // Zustandストアからプロパティを取得
  const storeProps = useGachaItemStore();
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  // propsとストアの値をマージ（propsが優先）
  const itemFiles = props.itemFiles || storeProps.itemFiles;

  // 全てのアイテムがないか、空の配列の場合は何も表示しない
  if (!itemFiles || itemFiles.length === 0) {
    return null;
  }
  const handleItemClick = (index: number) => {
    setSelectedIndex(index);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  return (
    <>
      <GachaCollection
        isPreview={isPreview}
        itemFiles={itemFiles}
        hasBg={true}
        onItemClick={handleItemClick}
        hasPlaceholder
      />
      {!isPreview && (
        <GachaReviewModal
          isOpen={modalOpen}
          onClose={handleCloseModal}
          initialIndex={selectedIndex}
          itemFiles={itemFiles}
          itemId={Number(itemId)}
        />
      )}
    </>
  );
};

export default GachaCollectionSection;
