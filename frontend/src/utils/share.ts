export type InvokeNativeShareParams = {
  text: string;
};

export async function navigateShare({ text }: InvokeNativeShareParams): Promise<void> {
  try {
    if (typeof navigator !== 'undefined' && navigator.share) {
      // MEMO: Web Share APIはセキュアコンテキスト(通常はHTTPS)でしか使えない
      // そのため、ローカル開発環境では動作しない
      await navigator.share({ text });
    }
  } catch (error) {
    console.error('ショップページのシェア実行中にエラーが発生しました:', error);
  }
}
