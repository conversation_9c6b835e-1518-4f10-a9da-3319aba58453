package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.Util.*
import jp.co.torihada.fanme.modules.shop.models.*
import org.jboss.logging.Logger

@ApplicationScoped
class PostOrderProcessor {
    @Inject lateinit var logger: Logger

    data class Input(
        val userId: String,
        val shop: Shop,
        val transaction: Transaction?,
        val cartId: Long,
        val checkoutId: Long,
        val cartItemIds: List<Long>,
        val amounts: OrderAmounts,
        val purchasedItemStatus: PurchasedItemStatus,
        val paymentMethod: PaymentMethod,
    )

    data class Output(val order: Order, val purchasedItems: List<PurchasedItem>)

    fun execute(params: Input): Result<Output, FanmeException> {
        // オーダーの更新
        val order =
            Order.updateOrder(
                checkoutId = params.checkoutId,
                transactionId = params.transaction?.id,
            )
        if (order == null) {
            return Err(ResourceNotFoundException("Order"))
        }

        // 購入商品の登録
        val cart = Cart.findById(params.cartId) ?: return Err(ResourceNotFoundException("Cart"))

        if (params.paymentMethod != PaymentMethod.CREDIT_CARD) {
            val cartItems = cart.items.filter { params.cartItemIds.contains(it.id!!) }
            cartItems.map { cartItem ->
                PurchasedItem.create(
                    orderId = order.id!!,
                    purchaserUid = params.userId,
                    itemId = cartItem.item.id!!,
                    itemFileId = cartItem.singleFile?.id,
                    price =
                        params.amounts.unitPrices.find { it.item.id == cartItem.item.id }!!.price,
                    quantity = cartItem.quantity,
                    status = params.purchasedItemStatus.value,
                    purchaserComment = cartItem.purchaserComment,
                )
            }
        }

        if (params.paymentMethod == PaymentMethod.PAY_PAY) {
            // PayPayの場合はitemを削除せずカートをロック
            Cart.lock(params.cartId)
        } else if (params.paymentMethod == PaymentMethod.CREDIT_CARD) {
            // なにもしない
        } else {
            CartItem.deleteItems(params.cartItemIds)
        }

        // メール送信
        val purchasedItems = PurchasedItem.findByOrderId(order.id!!)

        return Ok(Output(order, purchasedItems))
    }
}
