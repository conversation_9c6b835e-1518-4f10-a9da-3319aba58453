package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class AuditGroupMetadata(
    var shopId: Long? = null,
    var itemId: String? = null,
    var title: String? = null,
    var description: String? = null,
)

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "audit_groups")
class AuditGroup : BaseModel() {
    enum class AuditType(val value: String) {
        SHOP("SHOP"),
        SHOP_ITEM("SHOP_ITEM"),
        FANME_PROFILE("FANME_PROFILE"),
        FANME_CONTENT("FANME_CONTENT"),
    }

    enum class OperationType(val value: String) {
        INSERT("INSERT"),
        UPDATE("UPDATE");

        companion object {
            fun fromValue(value: String): OperationType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid OperationType value: $value")
            }
        }
    }

    enum class AuditStatus(val value: Int) {
        UNAUDITED(0), // 未監査
        REJECTED(-1), // 却下
        PENDING(1), // PENDING
        RESEND(5), // 再提出
        APPROVED(9), // 承認
    }

    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinColumn(name = "audit_group_id")
    var auditObjects: MutableList<AuditObject> = mutableListOf()

    @Size(max = 50) @NotNull @Column(name = "user_id", nullable = false) var userUid: String = ""

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "audit_type", nullable = false)
    var auditType: AuditType = AuditType.SHOP_ITEM

    // INSERT, UPDATE
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false)
    var operationType: OperationType = OperationType.INSERT

    // json形式（shop_id,item_id,title,description,など動的に入る）
    @Column(name = "metadata", columnDefinition = "json") var metadata: String? = null

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "status", nullable = false)
    var status: AuditStatus = AuditStatus.UNAUDITED

    // 監査コメント
    @Column(name = "comment", columnDefinition = "text") var comment: String? = null

    // 監査結果
    @Column(name = "audited_at") var auditedAt: LocalDateTime? = null

    // 監査ユーザID
    @Size(max = 50) @Column(name = "audited_user_id") var auditedUserUid: String? = null

    companion object : PanacheCompanion<AuditGroup> {
        fun getMetadata(id: Long): AuditGroupMetadata {
            val auditGroup = findById(id)
            return jacksonObjectMapper().readValue(auditGroup?.metadata ?: "{}")
        }

        fun updateMetadata(id: Long, block: (AuditGroupMetadata) -> AuditGroupMetadata) {
            val auditGroup = findById(id)
            if (auditGroup != null) {
                val currentMetadata =
                    jacksonObjectMapper().readValue<AuditGroupMetadata>(auditGroup.metadata ?: "{}")
                val updatedMetadata = block(currentMetadata)
                auditGroup.metadata = jacksonObjectMapper().writeValueAsString(updatedMetadata)
                auditGroup.persist()
            }
        }
    }
}
