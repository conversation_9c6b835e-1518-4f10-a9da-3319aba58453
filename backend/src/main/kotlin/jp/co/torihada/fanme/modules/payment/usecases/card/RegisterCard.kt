package jp.co.torihada.fanme.modules.payment.usecases.card

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoCardClient
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoMemberClient
import jp.co.torihada.fanme.modules.payment.externals.entity.card.SavedCard
import jp.co.torihada.fanme.modules.payment.externals.entity.member.SaveMember
import jp.co.torihada.fanme.modules.payment.externals.entity.member.SearchMember
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class RegisterCard {

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject private lateinit var paymentConfig: PaymentConfig

    @Inject @RestClient private lateinit var extGmoCardClient: ExtGmoCardClient
    @Inject @RestClient private lateinit var extGmoMemberClient: ExtGmoMemberClient

    @Inject private lateinit var logger: Logger

    data class Input(val userId: String, val cardName: String, val token: String)

    data class Card(val cardSeq: Int, val cardNo: String)

    fun execute(params: Input): Result<Card, FanmeException> {

        // GMO member登録確認
        try {
            extGmoMemberClient.searchMember(
                SearchMember.Request(
                    siteId = paymentConfig.gmoSiteId(),
                    sitePass = paymentConfig.gmoSitePass(),
                    memberId = params.userId + "@" + commonConfig.tenant(),
                )
            )
        } catch (e: ClientWebApplicationException) {
            val errorResponseBody = e.response.readEntity(String::class.java)
            val objectMapper = ObjectMapper().registerKotlinModule()
            val errorResponses: List<SearchMember.ErrorResponse> =
                objectMapper.readValue(errorResponseBody)

            val errorInfoList = errorResponses.map { it.errInfo }

            // 会員登録がない場合 会員登録を行う
            if (errorInfoList.contains("E01390002")) {
                try {
                    extGmoMemberClient.saveMember(
                        SaveMember.Request(
                            siteId = paymentConfig.gmoSiteId(),
                            sitePass = paymentConfig.gmoSitePass(),
                            memberId = params.userId + "@" + commonConfig.tenant(),
                        )
                    )
                } catch (e: ClientWebApplicationException) {
                    val errorResponseBody = e.response.readEntity(String::class.java)
                    val objectMapper = ObjectMapper().registerKotlinModule()
                    val errorResponses: List<SaveMember.ErrorResponse> =
                        objectMapper.readValue(errorResponseBody)

                    errorResponses.forEach { errorResponse ->
                        val errorCode = errorResponse.errCode
                        val errorInfo = errorResponse.errInfo
                        logger.error(
                            "Failed to save member: ErrorCode: $errorCode, ErrorInfo: $errorInfo"
                        )
                    }

                    return Err(FanmeException(code = 0, message = "Failed to save member"))
                }
            } else {
                return Err(FanmeException(code = 0, message = "Failed to search member"))
            }
        } catch (e: Exception) {
            logger.error("Failed to search member: ${e.message}", e)
            return Err(FanmeException(code = 0, message = "Failed to search member"))
        }

        val input =
            SavedCard.Request(
                siteId = paymentConfig.gmoSiteId(),
                sitePass = paymentConfig.gmoSitePass(),
                memberId = params.userId + "@" + commonConfig.tenant(),
                token = params.token,
                cardName = params.cardName,
            )

        try {
            val result = extGmoCardClient.savedCard(input)
            return Ok(Card(cardSeq = result.cardSeq?.toInt() ?: 1, cardNo = result.cardNo ?: ""))
        } catch (e: ClientWebApplicationException) {
            val errorResponseBody = e.response.readEntity(String::class.java)
            val objectMapper = ObjectMapper().registerKotlinModule()
            val errorResponses: List<SavedCard.ErrorResponse> =
                objectMapper.readValue(errorResponseBody)

            errorResponses.forEach { errorResponse ->
                val errorCode = errorResponse.errCode
                val errorInfo = errorResponse.errInfo
                logger.error("Failed to update card: ErrorCode: $errorCode, ErrorInfo: $errorInfo")
            }

            return Err(FanmeException(code = 0, message = "Failed to update card"))
        } catch (e: Exception) {
            logger.error("save card connection error: ${e.message}", e)
            return Err(FanmeException(code = 0, message = "Failed to update card"))
        }
    }
}
