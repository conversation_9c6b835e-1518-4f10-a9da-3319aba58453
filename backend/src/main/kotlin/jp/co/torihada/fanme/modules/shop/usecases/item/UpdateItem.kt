package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService

@ApplicationScoped
class UpdateItem {

    @Inject private lateinit var shopAuditService: ShopAuditService

    data class Input(
        val itemId: Long,
        val creatorUid: String,
        val name: String,
        val description: String?,
        val thumbnailUri: String,
        val thumbnailFrom: Int,
        val thumbnailBlurLevel: Int,
        val thumbnailWatermarkLevel: Int,
        val price: Int,
        val available: Boolean,
        val files: List<File>?,
        val samples: List<File>?,
        val benefit: BenefitParam?,
        val tags: List<String>?,
        val itemOption: ItemOptionParam,
    )

    data class File(
        val id: Long?,
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val price: Int?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean = false,
        val sortOrder: Int = 0,
    )

    data class BenefitParam(val description: String?, val files: List<File>)

    data class ItemOptionParam(
        val isSingleSales: Boolean,
        val qtyTotal: Int?,
        val qtyPerUser: Int?,
        val forSale: ForSale?,
        val password: String?,
        val onSale: OnSale?,
    )

    data class ForSale(val startAt: Instant?, val endAt: Instant?)

    data class OnSale(val discountRate: Float, val startAt: Instant?, val endAt: Instant?)

    fun execute(params: Input): Result<Item, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))

        // itemIdが存在するか確認
        // itemTypeがガチャ以外か確認
        var item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        val isTargetType = item.itemType in listOf(ItemType.DIGITAL_BUNDLE, ItemType.CHEKI)
        if (!isTargetType) {
            return Err(ResourceNotFoundException("Item"))
        }

        item =
            Item.update(
                params.itemId,
                params.name,
                params.description,
                params.thumbnailUri,
                params.thumbnailFrom,
                params.thumbnailBlurLevel,
                params.thumbnailWatermarkLevel,
                params.price,
                0,
                params.available,
            )

        item.files.forEach() {
            if (params.files?.find { f -> f.id == it.id } == null) {
                ItemFile.deleteById(it.id!!)
            }
        }
        val files =
            params.files?.map {
                if (it.id != null) {
                    val file = ItemFile.findById(it.id)
                    if (file != null) {
                        ItemFile.update(
                            it.id,
                            it.name,
                            it.objectUri,
                            it.thumbnailUri,
                            it.price,
                            it.fileType,
                            it.size,
                            it.duration,
                            it.itemThumbnailSelected,
                            it.sortOrder,
                        )
                    } else {
                        ItemFile.create(
                            item.id!!,
                            it.name,
                            it.objectUri,
                            it.thumbnailUri,
                            it.price,
                            it.fileType,
                            it.size,
                            it.duration,
                            it.itemThumbnailSelected,
                            it.sortOrder,
                        )
                    }
                } else {
                    ItemFile.create(
                        item.id!!,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.price,
                        it.fileType,
                        it.size,
                        it.duration,
                        it.itemThumbnailSelected,
                        it.sortOrder,
                    )
                }
            }
        val itemFileType = if (!files.isNullOrEmpty()) Util.getItemFileType(files) else 0
        item.fileType = itemFileType
        item.persist()
        ItemOption.updateByItemId(
            item.id!!,
            params.itemOption.isSingleSales,
            params.itemOption.qtyTotal,
            params.itemOption.qtyPerUser,
            params.itemOption.forSale?.startAt,
            params.itemOption.forSale?.endAt,
            params.itemOption.password,
        )

        val onSale = item.onSale
        params.itemOption.onSale?.let {
            if (onSale != null) {
                ItemOnSale.updateByItemId(item.id!!, it.discountRate, it.startAt, it.endAt)
            } else {
                ItemOnSale.create(item.id!!, it.discountRate, it.startAt, it.endAt)
            }
        }

        item.samples.forEach() {
            if (params.samples?.find { s -> s.id == it.id } == null) {
                Sample.deleteById(it.id!!)
            }
        }
        val samples =
            params.samples?.map { sampleParam ->
                Sample.upsert(
                    item.id!!,
                    sampleParam.id,
                    sampleParam.name,
                    sampleParam.objectUri,
                    sampleParam.thumbnailUri,
                    sampleParam.fileType,
                    sampleParam.size,
                    sampleParam.duration,
                )
            }
        var benefit = item.benefit
        var benefitFiles: List<BenefitFile>? = null
        if (params.benefit == null && benefit != null) {
            val deleteBenefitFiles = BenefitFile.findByBenefitId(benefit.id!!)
            deleteBenefitFiles.forEach { it.delete() }
            Benefit.deleteById(benefit.id!!)
        } else if (params.benefit != null) {
            if (benefit != null) {
                Benefit.update(item.id!!, params.benefit.description)
            } else {
                benefit = Benefit.create(item.id!!, params.benefit.description)
            }
            item.benefit?.files?.forEach() {
                if (params.benefit.files.find { f -> f.id == it.id } == null) {
                    benefit.files.remove(it)
                }
            }
            if (params.benefit.files.isNotEmpty()) {
                benefitFiles = createBenefitFiles(benefit, params.benefit.files)
            }
        }
        item.tags.forEach() {
            if (params.tags?.find { t -> t == it.tag.tag } == null) {
                ItemTag.deleteById(it.id!!)
            }
        }
        params.tags?.forEach {
            if (item.tags.find { t -> t.tag.tag == it } == null) {
                Tag.create(shop.id!!, it)
            }
            ItemTag.create(item.id!!, it)
        }

        // 監査データの作成
        shopAuditService.createAuditDataForShopItem(
            params.creatorUid,
            ShopAuditService.OperationType.UPDATE,
            item,
            files,
            samples,
            benefitFiles,
        )

        return Ok(item)
    }

    private fun createBenefitFiles(benefit: Benefit, files: List<File>): List<BenefitFile>? {
        return files.map {
            if (it.id != null) {
                val benefitFile = BenefitFile.findById(it.id)
                if (benefitFile != null) {
                    BenefitFile.update(
                        it.id,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.fileType,
                        it.size,
                        it.duration,
                    )
                } else {
                    BenefitFile.create(
                        benefit.id!!,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.fileType,
                        it.size,
                        it.duration,
                    )
                }
            } else {
                BenefitFile.create(
                    benefit.id!!,
                    it.name,
                    it.objectUri,
                    it.thumbnailUri,
                    it.fileType,
                    it.size,
                    it.duration,
                )
            }
        }
    }
}
