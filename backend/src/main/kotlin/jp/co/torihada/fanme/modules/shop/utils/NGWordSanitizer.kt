package jp.co.torihada.fanme.modules.shop.utils

import jakarta.inject.Inject
import java.util.regex.Pattern
import org.jboss.logging.Logger

object NGWordSanitizer {
    @Inject lateinit var logger: Logger

    private val NG_WORDS_REGEXP by lazy {
        try {
            val escapedWords =
                NGWordsConst.NG_WORDS.sortedByDescending { it.length }
                    .map { word -> Pattern.quote(word) }
            escapedWords.joinToString("|").toRegex(RegexOption.IGNORE_CASE)
        } catch (e: Exception) {
            logger.error("Failed to compile NG words regexp", e)
            "".toRegex()
        }
    }

    /**
     * 文字列内のNG用語をマスキング
     *
     * @return マスキング処理後の文字列
     */
    fun String?.maskNGWords(): String? {
        return maskNGWordsWithCondition(applyMasking = true, text = this)
    }

    /**
     * 条件付きで文字列内のNG用語をマスキング
     *
     * @param applyMasking マスキングを適用するかどうか
     * @param text マスキング対象の文字列
     * @return マスキング処理後の文字列
     */
    fun maskNGWordsWithCondition(applyMasking: <PERSON>ole<PERSON>, text: String?): String? {
        if (text.isNullOrEmpty() || !applyMasking) return text

        return try {
            if (!NG_WORDS_REGEXP.containsMatchIn(text)) return text
            NG_WORDS_REGEXP.replace(text) { "***" }
        } catch (e: Exception) {
            if (::logger.isInitialized) {
                logger.error("Error masking NG words", e)
            }
            text
        }
    }

    /**
     * 条件付きで文字列内のNG用語をマスキングする拡張関数
     *
     * @param applyMasking マスキングを適用するかどうか
     * @return マスキング処理後の文字列
     */
    fun String?.maskNGWords(applyMasking: Boolean): String? {
        return maskNGWordsWithCondition(applyMasking, this)
    }
}
