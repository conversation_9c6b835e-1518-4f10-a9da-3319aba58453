package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.EntryTransaction
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.AccountSearch
import jp.co.torihada.fanme.modules.payment.services.ErrorResponse
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class GetTransferAccount {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var config: Config
    @Inject @RestClient private lateinit var extGmoTransferClient: ExtGmoTransferClient

    data class Input(val userId: String)

    data class Output(
        val deleteFlag: String? = null,
        val bankName: String? = null,
        val bankCode: String? = null,
        val branchName: String? = null,
        val branchCode: String? = null,
        val accountNumber: String? = null,
        val accountName: String? = null,
        val branchCodeJpbank: String? = null,
        val accountNumberJpbank: String? = null,
    )

    fun execute(params: Input): Result<Output, ErrorResponse> {
        val bankId = params.userId

        try {
            val response =
                extGmoTransferClient.accountSearch(
                    AccountSearch.Request(
                        shopId = config.gmoTransferShopId(),
                        shopPass = config.gmoTransferShopPass(),
                        bankId = bankId,
                    )
                )

            return Ok(
                Output(
                    deleteFlag = response.deleteFlag,
                    bankName = response.bankName,
                    bankCode = response.bankCode,
                    branchName = response.branchName,
                    branchCode = response.branchCode,
                    accountNumber = response.accountNumber,
                    accountName = response.accountName,
                    branchCodeJpbank = response.branchCodeJpbank,
                    accountNumberJpbank = response.accountNumberJpbank,
                )
            )
        } catch (e: ClientWebApplicationException) {

            // e.response.entity.toString()を試す
            val errorResponseBody = e.response.readEntity(String::class.java)
            val objectMapper = ObjectMapper().registerKotlinModule()
            val errorResponses: List<EntryTransaction.ErrorResponse> =
                objectMapper.readValue(errorResponseBody)

            // エラーをログに記録
            logger.error("Failed to get transfer account userId: ${params.userId}")
            errorResponses.forEach { errorResponse ->
                val errorCode = errorResponse.errCode
                val errorInfo = errorResponse.errInfo
                logger.error("ErrorCode: $errorCode, ErrorInfo: $errorInfo")
            }

            return Ok(Output())
        } catch (e: Exception) {
            logger.error("get transfer account connection error: ${e.message}", e)
            return Ok(Output())
        }
    }
}
