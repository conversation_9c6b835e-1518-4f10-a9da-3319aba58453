package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.shop.models.Order

@ApplicationScoped
class UpdateOrder {
    data class Input(val transactionId: Long?, val checkout: Checkout, val status: String)

    fun execute(params: Input): Result<Order, FanmeException> {
        if (Order.findByCheckoutId(params.checkout.id!!) == null)
            return Err(ResourceNotFoundException("Order"))
        val order =
            Order.updateOrder(params.checkout.id!!, params.transactionId)
                ?: return Err(ResourceNotFoundException("Order"))

        return Ok(order)
    }
}
