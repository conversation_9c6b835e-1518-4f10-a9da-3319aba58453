package jp.co.torihada.fanme.modules.shop.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull

@PersistenceUnit(name = "cart_item")
@Entity
@Table(name = "cart_items")
class CartItem : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "cart_id", nullable = false, updatable = false)
    var cart: Cart = Cart()

    @NotNull
    @ManyToOne
    @JoinColumn(name = "item_id", nullable = false, updatable = false)
    var item: Item = Item()

    @ManyToOne
    @JoinColumn(name = "single_file_id", nullable = true, updatable = false)
    var singleFile: ItemFile? = ItemFile()

    @Min(1) @Column var quantity: Int = 1

    @Column(name = "purchaser_comment", columnDefinition = "TEXT")
    var purchaserComment: String? = null

    companion object : PanacheCompanion<CartItem> {
        fun findByForeignKey(cartId: Long, itemId: Long, singleFileId: Long?): CartItem? {
            return if (singleFileId == null) {
                find("cart.id = ?1 and item.id = ?2 and singleFile is null", cartId, itemId)
                    .firstResult()
            } else {
                find(
                        "cart.id = ?1 and item.id = ?2 and singleFile.id = ?3",
                        cartId,
                        itemId,
                        singleFileId,
                    )
                    .firstResult()
            }
        }

        fun findByCartId(cartId: Long): List<CartItem> {
            return find("cart.id", cartId).list()
        }

        fun create(cartId: Long, itemId: Long, quantity: Int, singleFileId: Long?): CartItem {
            val cartItem = CartItem()
            cartItem.cart = Cart.findById(cartId)!!
            cartItem.item = Item.findById(itemId)!!
            cartItem.singleFile =
                if (singleFileId != null) ItemFile.findById(singleFileId) else null
            cartItem.quantity = quantity
            cartItem.persist()
            return cartItem
        }

        fun update(id: Long, quantity: Int?, purchaserComment: String?): CartItem {
            val cartItem = findById(id)!!
            quantity?.let { cartItem.quantity = it }
            purchaserComment?.let { cartItem.purchaserComment = it }
            cartItem.persist()
            return cartItem
        }

        fun delete(id: Long): Long {
            return delete("id", id)
        }

        fun deleteItems(ids: List<Long>) {
            delete("id in ?1", ids)
        }
    }
}
