package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import jp.co.torihada.fanme.modules.payment.services.ErrorResponse
import org.jboss.logging.Logger

@ApplicationScoped
class GetTransferInfo {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var config: Config

    data class Input(val userId: String)

    data class Output(
        val pendingAmounts: List<MonthAndAmount>,
        val notAppliedAmounts: List<MonthAndAmount>,
        val appliedDetailAmounts: List<AppliedDetail>,
        val balanceAmount: Int,
        val transferState: Boolean,
    )

    data class AppliedDetail(val appliedAmounts: List<MonthAndAmount>, val fee: Int)

    data class MonthAndAmount(
        var yearMonth: String,
        var amount: Int,
        var latestExpireDate: LocalDateTime? = null,
    )

    private val tenantFee = 440

    fun execute(params: Input): Result<Output, ErrorResponse> {
        val dateFormatter = DateTimeFormatter.ofPattern("yyyyMM")
        val now =
            if (config.envKind() == "test") {
                LocalDateTime.of(2024, 3, 1, 0, 0)
            } else {
                LocalDateTime.now(ZoneOffset.UTC)
            }

        // 失効期限が150日なので、6ヶ月前のデータを取得する
        val thisYearmonth = dateFormatter.format(now)
        val previousYearMonth = dateFormatter.format(now.minusMonths(1))
        val minYearMonth = dateFormatter.format(now.minusMonths(6))

        // 暫定売上データの取得
        // 今月のデータ
        val thisMonthSellerSales =
            MonthlySellerSale.findBySellerUserIdAndYearMonth(params.userId, thisYearmonth)

        // 前月のデータ
        // 毎月15日になるまで売上が確定しないため先月の暫定売上を取得
        val previousMonthSellerSales =
            MonthlySellerSale.findBySellerUserIdAndYearMonth(
                params.userId,
                previousYearMonth,
                merged = false,
            )

        val pendingList =
            listOfNotNull(
                previousMonthSellerSales?.let {
                    MonthAndAmount(it.yearMonth!!, it.remainingAmount!!)
                },
                thisMonthSellerSales?.let { MonthAndAmount(it.yearMonth!!, it.remainingAmount!!) },
            )

        // 売上確定済みのデータを取得
        val monthlySellerSaleList =
            MonthlySellerSale.findBySellerUserIdAndRemainingAmountAndMiniYearmonth(
                tenant = config.tenant(),
                sellerUserId = params.userId,
                minYearMonth = minYearMonth,
                merged = true,
                remainingAmount = 0,
            )

        val monthAndAmountList =
            monthlySellerSaleList.map {
                val japanZoneId = ZoneId.of("Asia/Tokyo")
                val localDateTime =
                    it.expirationDate?.let { expirationDate ->
                        ZonedDateTime.ofInstant(expirationDate, japanZoneId).toLocalDateTime()
                    }
                MonthAndAmount(it.yearMonth!!, it.remainingAmount!!, localDateTime)
            }

        // 現在の送金申請状態を取得
        val sellerGmoTransfers =
            SellerGmoTransfer.findByStatusesAndSellerUserId(
                listOf(
                    Const.GmoTransferStatus.TransferNotRegistered.value,
                    Const.GmoTransferStatus.TransferRegistered.value,
                    Const.GmoTransferStatus.TransferDataRegistered.value,
                ),
                params.userId,
            )

        // 申請済みのデータ
        val appliedListList = mutableListOf<List<MonthAndAmount>>()
        // 未申請のデータ
        val notAppliedList = mutableListOf<MonthAndAmount>()
        if (sellerGmoTransfers.isEmpty()) {
            // TODO itaratorを使わない方法に変更したい
            val monthAndAmountIterator = monthAndAmountList.iterator()
            var tempMonthAndAmount = MonthAndAmount("dummy", 0) // 初期値
            sellerGmoTransfers.forEach { sellerGmoTransfer ->
                // 申請中のデータが送金完了した場合は440円の手数料が追加されるので考慮する必要がある
                var transferAmountPlusFee = sellerGmoTransfer.amount + tenantFee
                val appliedList = mutableListOf<MonthAndAmount>()

                // この送金額が何ヶ月文のものなのか確認する
                while (monthAndAmountIterator.hasNext()) {
                    // 月の売上が０円の場合はスキップ
                    tempMonthAndAmount =
                        if (tempMonthAndAmount.amount == 0) {
                            monthAndAmountIterator.next()
                        } else {
                            tempMonthAndAmount
                        }

                    if (tempMonthAndAmount.amount <= transferAmountPlusFee) {
                        appliedList.add(
                            MonthAndAmount(tempMonthAndAmount.yearMonth, tempMonthAndAmount.amount)
                        )
                        transferAmountPlusFee -= tempMonthAndAmount.amount
                        tempMonthAndAmount.amount = 0

                        // 送金額が0円になった場合は申請済みリストに追加
                        if (transferAmountPlusFee == 0) {
                            appliedListList.add(appliedList)
                            break
                        }
                    } else {
                        // 1ヶ月の売上金が複数の送金に分割された場合
                        appliedList.add(
                            MonthAndAmount(tempMonthAndAmount.yearMonth, transferAmountPlusFee)
                        )
                        appliedListList.add(appliedList)

                        tempMonthAndAmount.amount -= transferAmountPlusFee

                        // next()で次の月へ進めない
                        break
                    }
                }
            }

            // 残っている毎月の金額は未申請
            if (tempMonthAndAmount.amount > 0) {
                notAppliedList.add(tempMonthAndAmount)
            }

            while (monthAndAmountIterator.hasNext()) {
                val monthAndAmount = monthAndAmountIterator.next()
                notAppliedList.add(monthAndAmount)
            }
        } else {
            // 申請中のデータがない場合
            monthAndAmountList.forEach { it ->
                notAppliedList.add(MonthAndAmount(it.yearMonth, it.amount, it.latestExpireDate))
            }
        }

        // クリエイター６ヶ月以内稼いだ現在引き出せる金額金額（暫定の当月分も含む）
        val balanceAmount =
            MonthlySellerSale.findRemainingAmountSumBySellerAndTenant(
                tenant = config.tenant(),
                sellerUserId = params.userId,
            )

        // 現在送金できる状態かどうか
        // 申請中、送金中のデータがある場合は送金できない
        val transferState = sellerGmoTransfers.isEmpty()

        return Ok(
            Output(
                pendingAmounts = pendingList.ifEmpty { emptyList() },
                notAppliedAmounts = notAppliedList.ifEmpty { emptyList() },
                appliedDetailAmounts =
                    appliedListList.map { AppliedDetail(it, tenantFee) }.ifEmpty { emptyList() },
                balanceAmount = balanceAmount,
                transferState = transferState,
            )
        )
    }
}
