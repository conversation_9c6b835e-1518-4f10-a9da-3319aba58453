package jp.co.torihada.fanme.modules.shop.services.aws

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.Duration
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util
import org.jboss.logging.Logger
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest

@ApplicationScoped
class S3 {

    @Inject lateinit var s3Presigner: S3Presigner

    @Inject lateinit var config: Config

    @Inject lateinit var logger: Logger

    fun checkFileExists(fileId: Long): String {
        return "file_exists"
    }

    fun getPresignedUrl(key: String, type: Util.PresignedUrlType, name: String?): String {
        val bucket = config.s3BucketName()
        try {
            when (type) {
                Util.PresignedUrlType.DOWNLOAD -> {
                    val encodedFileName = URLEncoder.encode(name, StandardCharsets.UTF_8.toString())
                    val request =
                        GetObjectPresignRequest.builder()
                            .signatureDuration(Duration.ofMinutes(5))
                            .getObjectRequest(
                                if (name == null)
                                    GetObjectRequest.builder().bucket(bucket).key(key).build()
                                else
                                    GetObjectRequest.builder()
                                        .bucket(bucket)
                                        .key(key)
                                        .responseContentDisposition(
                                            "attachment; filename=\"$encodedFileName\"; filename*=UTF-8''$encodedFileName"
                                        )
                                        .build()
                            )
                            .build()
                    return s3Presigner.presignGetObject(request).url().toString()
                }
                Util.PresignedUrlType.UPLOAD -> {
                    val request =
                        PutObjectPresignRequest.builder()
                            .signatureDuration(Duration.ofMinutes(10))
                            .putObjectRequest { it.bucket(bucket).key(key) }
                            .build()
                    return s3Presigner.presignPutObject(request).url().toString()
                }
                Util.PresignedUrlType.GET_OBJECT -> {
                    val request =
                        GetObjectPresignRequest.builder()
                            .signatureDuration(Duration.ofDays(1))
                            .getObjectRequest(
                                GetObjectRequest.builder().bucket(bucket).key(key).build()
                            )
                            .build()
                    return s3Presigner.presignGetObject(request).url().toString()
                }
            }
        } catch (e: Exception) {
            throw FanmeException(1000, e.message ?: "Failed to get presigned url")
        }
    }

    fun getObjectUri(fileUri: String): String {
        val s3BucketPrefix = "${config.s3Endpoint()}/${config.s3BucketName()}"
        if (fileUri.startsWith(s3BucketPrefix)) {
            if (fileUri.startsWith("/public/")) {
                return fileUri
            }
            val fileUriWithoutParams = fileUri.split("?")[0]
            val objectKey = fileUriWithoutParams.replace("$s3BucketPrefix/", "")
            return getPresignedUrl(objectKey, Util.PresignedUrlType.GET_OBJECT, null)
        } else {
            return fileUri
        }
    }
}
