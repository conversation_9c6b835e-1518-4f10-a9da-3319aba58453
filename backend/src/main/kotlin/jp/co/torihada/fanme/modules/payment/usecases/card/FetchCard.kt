package jp.co.torihada.fanme.modules.payment.usecases.card

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoCardClient
import jp.co.torihada.fanme.modules.payment.externals.entity.card.SearchCard
import jp.co.torihada.fanme.modules.payment.models.PurchaserDefaultCard
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class FetchCard {

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject private lateinit var paymentConfig: PaymentConfig

    @Inject @RestClient private lateinit var extGmoCardClient: ExtGmoCardClient

    @Inject private lateinit var logger: Logger

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Card(
        val cardSequence: Int?,
        val defaultFlag: String?,
        val name: String?,
        val number: String?,
        val expiresAt: String?,
        val holderName: String?,
        val brand: String?,
        val lastUsed: Boolean,
    )

    fun execute(userId: String): Result<List<Card?>, FanmeException> {

        val input =
            SearchCard.Request(
                siteId = paymentConfig.gmoSiteId(),
                sitePass = paymentConfig.gmoSitePass(),
                memberId = userId + "@" + commonConfig.tenant(),
                seqMode = "0",
            )

        val defaultFlag = PurchaserDefaultCard.findByPurchaserUserId(userId)

        try {
            val response = extGmoCardClient.searchCard(input)

            return if (response.isEmpty()) {
                Ok(emptyList())
            } else {
                val oneCard = response.size == 1
                Ok(
                    response.map {
                        Card(
                            cardSequence = it.cardSeq,
                            defaultFlag = it.defaultFlag,
                            name = it.cardName,
                            number = it.cardNo,
                            expiresAt = it.expire,
                            holderName = it.holderName,
                            brand = it.brand,
                            lastUsed =
                                if (oneCard) defaultFlag?.cardSequence == it.cardSeq.toString()
                                else false,
                        )
                    }
                )
            }
        } catch (e: ClientWebApplicationException) {
            val errorResponseBody = e.response.readEntity(String::class.java)
            val objectMapper = ObjectMapper().registerKotlinModule()
            val errorResponses: List<SearchCard.ErrorResponse> =
                objectMapper.readValue(errorResponseBody)

            val errorInfoList = mutableListOf<String?>()
            errorResponses.forEach { errorResponse ->
                val errorCode = errorResponse.errCode
                val errorInfo = errorResponse.errInfo
                errorInfoList.add(errorInfo)
                logger.error("Failed to search card: ErrorCode: $errorCode, ErrorInfo: $errorInfo")
            }

            // ErrorInfo: E01390002, E01240002
            if (errorInfoList.contains("E01390002") || errorInfoList.contains("E01240002")) {
                return Ok(emptyList())
            }

            return Err(FanmeException(code = 0, message = "Failed to search card"))
        } catch (e: Exception) {
            logger.error("search card connection error: ${e.message}", e)
            return Err(FanmeException(code = 0, message = "Failed to search card"))
        }
    }
}
