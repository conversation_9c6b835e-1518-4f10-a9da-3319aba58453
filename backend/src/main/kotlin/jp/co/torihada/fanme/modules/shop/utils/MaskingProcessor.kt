package jp.co.torihada.fanme.modules.shop.utils

import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.annotations.Maskable
import kotlin.reflect.KMutableProperty
import kotlin.reflect.KProperty
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties
import kotlin.reflect.full.primaryConstructor
import org.jboss.logging.Logger

/**
 * マスキング処理を行うユーティリティクラス
 *
 * @Maskableアノテーションが付与されたフィールドに対してNGワードマスキングを適用する
 */
object MaskingProcessor {
    @Inject lateinit var logger: Logger

    /**
     * 対象オブジェクトの@Maskableアノテーションが付与されたフィールドに マスキング処理を適用する
     *
     * @param target マスキング対象のオブジェクト
     * @return マスキング処理後の新しいオブジェクト
     */
    fun <T : Any> process(target: T): T {
        return try {
            processObject(target)
        } catch (e: Exception) {
            if (::logger.isInitialized) {
                logger.error("Error during masking process", e)
            }
            target
        }
    }

    /** オブジェクトに含まれる全てのプロパティを再帰的に処理する */
    private fun <T : Any> processObject(obj: T): T {
        val clazz = obj::class

        return when {
            clazz.isData -> processDataClass(obj)
            else -> processMutableObject(obj)
        }
    }

    private fun <T : Any> processDataClass(obj: T): T {
        val clazz = obj::class
        val constructor = clazz.primaryConstructor!!
        val parameters = constructor.parameters
        val arguments =
            clazz.memberProperties.associate { property ->
                val value = property.getter.call(obj)
                property.name to processValue(property, value)
            }

        @Suppress("UNCHECKED_CAST")
        return constructor.callBy(parameters.associateWith { arguments[it.name] }) as T
    }

    private fun <T : Any> processMutableObject(obj: T): T {
        obj::class.memberProperties.forEach { property ->
            if (property is KMutableProperty<*>) {
                val value = property.getter.call(obj)
                property.setter.call(obj, processValue(property, value))
            }
        }
        return obj
    }

    /** 単一の値を処理する */
    private fun processValue(property: KProperty<*>, value: Any?): Any? {
        return try {
            when {
                isMaskableString(property, value) -> maskString(value as String?)
                value is Collection<*> -> processCollection(value)
                value is Map<*, *> -> processMap(value)
                value is Array<*> -> processArray(value)
                value != null && shouldProcessRecursively(value) -> processObject(value)
                else -> value
            }
        } catch (e: Exception) {
            logger.error("Error processing value for property ${property.name}", e)
            value
        }
    }

    private fun isMaskableString(property: KProperty<*>, value: Any?): Boolean {
        return property.findAnnotation<Maskable>() != null && value is String?
    }

    private fun maskString(value: String?): String? {
        if (value.isNullOrEmpty()) return value
        return NGWordSanitizer.maskNGWordsWithCondition(applyMasking = true, text = value)
    }

    private fun processCollection(collection: Collection<*>): Collection<*> {
        return collection.map { item ->
            if (item != null && shouldProcessRecursively(item)) processObject(item) else item
        }
    }

    private fun processMap(map: Map<*, *>): Map<*, *> {
        return map.mapValues { (_, item) ->
            if (item != null && shouldProcessRecursively(item)) processObject(item) else item
        }
    }

    private fun processArray(array: Array<*>): Array<*> {
        return array
            .map { item ->
                if (item != null && shouldProcessRecursively(item)) processObject(item) else item
            }
            .toTypedArray()
    }

    /** オブジェクトを再帰的に処理すべきかどうかを判定 */
    private fun shouldProcessRecursively(obj: Any): Boolean =
        when {
            obj::class.isData -> true
            else -> false
        }
}
