package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "restricted_accounts")
class RestrictedAccounts : BaseModel() {
    @ManyToOne @JoinColumn(name = "creator_id", nullable = false) var user: User? = null

    @Column(name = "restrict_type", nullable = false) var restrictType: Int = 0

    @Column(name = "start_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var startAt: Instant? = null

    @Column(name = "end_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var endAt: Instant? = null

    companion object : PanacheCompanion<RestrictedAccounts> {

        fun getByUserIdAndRestrictedType(restrictType: Int, userId: Long?): RestrictedAccounts? {
            if (userId == null) return null
            val now = Instant.now()
            return find(
                    "user.id = ?1 and restrictType = ?2 and startAt <= ?3 and endAt >= ?4",
                    userId,
                    restrictType,
                    now,
                    now,
                )
                .firstResult()
        }
    }
}
