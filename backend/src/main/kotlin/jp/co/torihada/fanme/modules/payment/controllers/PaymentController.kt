package jp.co.torihada.fanme.modules.payment.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.controllers.responses.PaymentResponse
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.CreateCheckout
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.GetUserDailyTotalTipsForSeller

@ApplicationScoped
class PaymentController {

    @Inject private lateinit var createCheckout: CreateCheckout

    @Inject private lateinit var getUserDailyTotalTipsForSeller: GetUserDailyTotalTipsForSeller

    @Transactional
    fun createCheckout(
        sellerUserId: String,
        purchaserUserId: String,
        totalAmount: Int,
        profit: Int,
        fee: Int,
        itemsAmount: Int,
        tipAmount: Int,
        paymentType: Const.PaymentType,
        isDigital: Boolean = true,
        convenience: Const.Conveniences?,
        deliveryFee: Int?,
        fanmeToken: String? = null,
        successUrl: String? = null,
        errorUrl: String? = null,
    ): Checkout {
        return createCheckout
            .execute(
                CreateCheckout.Input(
                    sellerUserId = sellerUserId,
                    purchaserUserId = purchaserUserId,
                    totalAmount = totalAmount,
                    profit = profit,
                    fee = fee,
                    deliveryFee = deliveryFee,
                    itemsAmount = itemsAmount,
                    tipAmount = tipAmount,
                    paymentType = paymentType,
                    convenience = convenience,
                    isDigital = isDigital,
                    fanmeToken = fanmeToken,
                    successUrl = successUrl,
                    errorUrl = errorUrl,
                )
            )
            .getOrElse { throw FanmeException(0, "failed to create checkout") }
    }

    fun getUserDailyTotalTipsForSeller(
        sellerUserId: String,
        purchaserUserId: String,
    ): PaymentResponse.GetUserDailyTotalTipsForSeller {
        val response =
            getUserDailyTotalTipsForSeller
                .execute(
                    GetUserDailyTotalTipsForSeller.Input(
                        purchaserUserId = purchaserUserId,
                        sellerUserId = sellerUserId,
                    )
                )
                .getOrElse {
                    throw FanmeException(0, "failed to get user daily total tips for seller")
                }

        return PaymentResponse.GetUserDailyTotalTipsForSeller(
            totalAmount = response.totalAmount,
            remainingAmount = response.remainingAmount,
        )
    }
}
