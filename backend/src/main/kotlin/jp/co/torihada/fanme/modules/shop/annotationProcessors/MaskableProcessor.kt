package jp.co.torihada.fanme.modules.shop.annotationProcessors

import jp.co.torihada.fanme.modules.shop.annotations.Maskable
import jp.co.torihada.fanme.modules.shop.utils.NGWordSanitizer
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties

/** @Maskableアノテーションが付与されたプロパティに対してマスキング処理を適用するプロセッサ */
object MaskableProcessor {
    /**
     * エンティティのプロパティをマスキング
     *
     * @param entity マスキング対象のエンティティ
     * @return マスキング処理後のエンティティ
     */
    inline fun <reified T : Any> process(entity: T): T {
        entity::class.memberProperties.forEach { property ->
            property.findAnnotation<Maskable>()?.let {
                if (property is KMutableProperty<*>) {
                    val value = property.getter.call(entity)
                    if (value is String?) {
                        if (!it.skipIfNull || value != null) {
                            val maskedValue =
                                NGWordSanitizer.maskNGWordsWithCondition(
                                    applyMasking = true,
                                    text = value,
                                ) ?: ""
                            try {
                                property.setter.call(entity, maskedValue)
                            } catch (e: Exception) {
                                // リフレクションでのセットに失敗した場合はスキップ
                            }
                        }
                    }
                }
            }
        }
        return entity
    }
}
