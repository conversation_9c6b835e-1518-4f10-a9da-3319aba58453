package jp.co.torihada.fanme.modules.shop.usecases.file

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.util.UUID
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import org.jboss.logging.Logger

@ApplicationScoped
class GetUploadUrls {

    @Inject lateinit var s3: S3
    @Inject lateinit var logger: Logger

    data class Input(
        val creatorUid: String,
        val metadataList: List<UploadFileMetadata>,
        val isPublic: Boolean,
    )

    data class UploadFileMetadata(val id: String, val name: String?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class UploadUrl(val id: String, val name: String, val url: String)

    fun execute(params: Input): Result<List<UploadUrl>, FanmeException> {
        return try {
            if (params.metadataList.isEmpty()) {
                return Err(FanmeException(0, "No file names"))
            }
            if (params.isPublic) {
                return Ok(
                    params.metadataList.map {
                        val fileNameWithUuid = generateUploadKey(it)
                        UploadUrl(
                            id = it.id,
                            name = it.name ?: "",
                            url =
                                s3.getPresignedUrl(
                                    "public/${params.creatorUid}/$fileNameWithUuid",
                                    Util.PresignedUrlType.UPLOAD,
                                    null,
                                ),
                        )
                    }
                )
            }
            val shop =
                Shop.findByCreatorUid(params.creatorUid)
                    ?: return Err(ResourceNotFoundException("Shop"))
            val uploadUrls =
                params.metadataList.map {
                    val fileNameWithUuid = generateUploadKey(it)
                    UploadUrl(
                        id = it.id,
                        name = it.name ?: "",
                        url =
                            s3.getPresignedUrl(
                                "${shop.id}/$fileNameWithUuid",
                                Util.PresignedUrlType.UPLOAD,
                                null,
                            ),
                    )
                }
            Ok(uploadUrls)
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }

    private fun generateUploadKey(file: UploadFileMetadata): String {
        val uuid = UUID.randomUUID().toString()
        if (file.name != null) {
            return "${uuid}_${file.name}"
        } else {
            val uuid = UUID.randomUUID().toString()
            return uuid
        }
    }
}
