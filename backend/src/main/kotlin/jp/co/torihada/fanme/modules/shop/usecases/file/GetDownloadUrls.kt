package jp.co.torihada.fanme.modules.shop.usecases.file

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.util.UUID
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.services.aws.S3

@ApplicationScoped
class GetDownloadUrls {

    @Inject lateinit var s3: S3

    data class Input(val itemId: Long, val metadataList: List<DownloadFileMetadata>)

    data class DownloadFileMetadata(val key: String, val name: String?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class DownloadUrl(val key: String, val url: String)

    fun execute(params: Input): Result<List<DownloadUrl>, FanmeException> {
        return try {
            val item =
                Item.findById(params.itemId)
                    ?: throw ResourceNotFoundException(404, "Item not found")
            val shop = item.shop
            val downloadUrls =
                params.metadataList.map {
                    val fileName = generateFileDownloadName(it)

                    DownloadUrl(
                        key = it.key,
                        url =
                            s3.getPresignedUrl(
                                "${shop.id}/${it.key}",
                                Util.PresignedUrlType.DOWNLOAD,
                                fileName,
                            ),
                    )
                }
            Ok(downloadUrls)
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }

    private fun generateFileDownloadName(file: DownloadFileMetadata): String {
        val uuid = UUID.randomUUID().toString()
        return if (file.name != null) {
            file.name
        } else {
            "${uuid}_${file.key}"
        }
    }
}
