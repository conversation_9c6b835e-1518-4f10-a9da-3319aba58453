package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import java.time.Instant
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem

@ApplicationScoped
class UpdatePurchasedItemStatus {
    data class Input(val orderId: Long, val status: String)

    class Output

    fun execute(params: Input): Result<Output, FanmeException> {
        if (PurchasedItem.findByOrderId(params.orderId).isEmpty())
            return Err(ResourceNotFoundException("PurchasedItem"))
        val purchasedItems = PurchasedItem.findByOrderId(params.orderId)
        purchasedItems.forEach { item ->
            item.status = params.status
            // PAYSUCCESS時のみpurchasedAtを更新
            item.purchasedAt =
                if (params.status == Const.CheckoutStatus.PAYSUCCESS.value) {
                    Instant.now()
                } else {
                    null
                }
            item.persistAndFlush()
        }

        return Ok(Output())
    }
}
