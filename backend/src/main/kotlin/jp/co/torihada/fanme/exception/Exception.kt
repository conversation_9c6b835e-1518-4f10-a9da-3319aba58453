package jp.co.torihada.fanme.exception

import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.modules.shop.Const.MAX_TIP
import jp.co.torihada.fanme.modules.shop.Const.MIN_TIP

data class ErrorObject(@field:NotNull val code: Int, val message: String)

open class FanmeException(code: Int, message: String) : Exception(message) {
    val errors = listOf(ErrorObject(code, message))
}

open class ConsoleException(code: Int, message: String) : FanmeException(code, message)

open class GmoApiException(val apiName: String, val errCode: String, val errInfo: String) :
    Exception(buildMessage(apiName, errCode, errInfo)) {

    companion object {
        private fun buildMessage(apiName: String, errCode: String, errorInfo: String): String {
            return "[GMO API: $apiName] failed. ErrCode: ${errCode}, , ErrInfo: ${errorInfo}"
        }
    }
}

class ResourceNotFoundException(code: Int, resourceName: String) :
    FanmeException(code, "$resourceName not found.") {
    constructor(resourceName: String) : this(1000, resourceName)
}

class ResourceAlreadyExistsException(code: Int, resourceName: String) :
    FanmeException(code, "$resourceName already exists.") {
    constructor(resourceName: String) : this(1001, resourceName)
}

class NotOwnerException(code: Int, resourceName: String) :
    FanmeException(code, "You are not the owner of this $resourceName.") {
    constructor(resourceName: String) : this(1002, resourceName)
}

class UnAuthorizedException : FanmeException(1000, "Unauthorized.")

class IncorrectPasswordException : FanmeException(1000, "Password is incorrect.")

class ContainUnavailableItemException : FanmeException(1000, "Contain unavailable item.")

class CartItemInvalidQuantityException : FanmeException(1000, "Invalid quantity of cart item.")

class OutOfStockException : FanmeException(1000, "Out of stock.")

class InvalidTipAmountException :
    FanmeException(1000, "Tip must be 0 or between $MIN_TIP and $MAX_TIP.")

class ShopIsClosedException : FanmeException(1000, "Shop is closed.")

class InvalidProviderShopIdException : FanmeException(1000, "Invalid provider shop ID.")

class SaleNotStartedYetException : FanmeException(1000, "Sale has not started yet.")

class SaleAlreadyEndedException : FanmeException(1000, "Sale has already ended.")

class CartSingleItemWithSetException :
    FanmeException(1012, "Cannot add single item when set item exists in cart.")

class CartSetItemWithSingleException :
    FanmeException(1013, "Cannot add set item when single items exist in cart.")

class CartSetItemAlreadyPurchasedException :
    FanmeException(1014, "Cannot add set item or single item because set item already purchased.")

class CartSingleItemAlreadyPurchasedException :
    FanmeException(1015, "Single item already purchased.")

class CartSetItemAlreadyCheckoutException :
    FanmeException(1017, "Cannot add set item or single item because set item already checkout.")

class CartSingleItemAlreadyCheckoutException :
    FanmeException(1018, "Single item already checkout.")

class CartSetItemOverPurchasableLimitException :
    FanmeException(1019, "Set item over purchasable limit. Cannot add more set items.")

class GmoSecureTran2Exception(errCode: String, errInfo: String) :
    GmoApiException(apiName = "SecureTran2", errCode = errCode, errInfo = errInfo)

class ItemTypeIsNotDigitalGachaException : FanmeException(1000, "ItemType is not DIGITAL_GACHA")

class DigitalGachaNotPurchasedException :
    FanmeException(
        1100,
        "Digital gacha not purchased. Please purchase the digital gacha before pulling.",
    )

class DigitalGachaPullCountExceededException :
    FanmeException(
        1101,
        "You have reached the digital gacha pull limit. Please check your remaining pulls.",
    )

class DigitalGachaInvalidPullCountException :
    FanmeException(
        1102,
        "The requested gacha pull count is not allowed. Please select a valid pull count.",
    )

class InvalidChekiAmountException :
    FanmeException(1200, "Cheki amount must be greater than or equal to the cheki cost.")

class PaymentServiceErrorException(message: String? = null) :
    FanmeException(1202, message ?: "Payment service error")

class ConsoleUserFetchException(message: String? = null) :
    ConsoleException(4000, message ?: "Failed to fetch console user")

class ConsoleResourceNotFoundException(message: String? = null) :
    ConsoleException(4001, message ?: "User not found in Console context.")

class ForbiddenAccessException(message: String? = null) :
    ConsoleException(4002, message ?: "Forbidden access")

class InvalidYearMonthFormatException(message: String? = null) :
    ConsoleException(4004, message ?: "Invalid YearMonth format. Expected format: YYYYMM")
