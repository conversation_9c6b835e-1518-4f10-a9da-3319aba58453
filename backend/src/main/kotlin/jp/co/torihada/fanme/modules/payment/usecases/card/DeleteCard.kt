package jp.co.torihada.fanme.modules.payment.usecases.card

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result as ResultType
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoCardClient
import kotlin.Boolean
import kotlin.Exception
import kotlin.Int
import kotlin.String
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class DeleteCard {

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject private lateinit var paymentConfig: PaymentConfig

    @Inject @RestClient private lateinit var extGmoCardClient: ExtGmoCardClient

    @Inject private lateinit var logger: Logger

    data class Input(val userId: String, val cardSequence: Int)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Result(val success: Boolean)

    fun execute(params: Input): ResultType<Result, FanmeException> {
        val input =
            jp.co.torihada.fanme.modules.payment.externals.entity.card.DeleteCard.Request(
                siteId = paymentConfig.gmoSiteId(),
                sitePass = paymentConfig.gmoSitePass(),
                memberId = params.userId + "@" + commonConfig.tenant(),
                cardSeq = params.cardSequence,
                seqMode = "0",
            )

        try {
            extGmoCardClient.deleteCard(input)
            return Ok(Result(success = true))
        } catch (e: ClientWebApplicationException) {
            val errorResponseBody = e.response.readEntity(String::class.java)
            val objectMapper = ObjectMapper().registerKotlinModule()
            val errorResponses:
                List<
                    jp.co.torihada.fanme.modules.payment.externals.entity.card.DeleteCard.ErrorResponse
                > =
                objectMapper.readValue(errorResponseBody)

            errorResponses.forEach { errorResponse ->
                val errorCode = errorResponse.errCode
                val errorInfo = errorResponse.errInfo
                logger.error("Failed to delete card: ErrorCode: $errorCode, ErrorInfo: $errorInfo")
            }

            return Err(FanmeException(code = 0, message = "Failed to delete card"))
        } catch (e: Exception) {
            logger.error("delete card connection error: ${e.message}", e)
            return Err(FanmeException(code = 0, message = "Failed to delete card"))
        }
    }
}
