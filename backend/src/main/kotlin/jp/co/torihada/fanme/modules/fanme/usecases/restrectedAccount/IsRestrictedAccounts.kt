package jp.co.torihada.fanme.modules.fanme.usecases.restrectedAccount

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.RestrictedAccounts
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class IsRestrictedAccounts {
    data class Input(val userUid: String, val restrictType: Int)

    fun execute(params: Input): Result<Bo<PERSON>an, FanmeException> {
        val user = User.findByUuid(params.userUid) ?: return Err(ResourceNotFoundException("User"))
        return Ok(
            RestrictedAccounts.getByUserIdAndRestrictedType(params.restrictType, user.id) != null
        )
    }
}
