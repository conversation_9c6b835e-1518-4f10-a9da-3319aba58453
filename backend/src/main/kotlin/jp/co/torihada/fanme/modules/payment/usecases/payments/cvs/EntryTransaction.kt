package jp.co.torihada.fanme.modules.payment.usecases.payments.cvs

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoPaymentClient
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.EntryTransaction
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.EntryTransactionCvs
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseEntryTransaction
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class EntryTransaction : BaseEntryTransaction() {

    @Inject lateinit var logger: Logger

    @Inject @RestClient private lateinit var extGmoPaymentClient: ExtGmoPaymentClient

    fun execute(params: Input): Result<Unit, FanmeException> {
        val checkout =
            Checkout.findById(params.checkoutId)
                ?: return Err(ResourceNotFoundException("Checkout"))
        try {
            val args = getEntryTransactionArgs(checkout)
            if (args.orderId != params.orderId) Err(FanmeException(100, "OrderId is not matched"))

            val response: EntryTransaction.Response =
                extGmoPaymentClient.entryTransactionCvs(
                    EntryTransactionCvs.Request(
                        shopId = args.shopId,
                        shopPass = args.shopPass,
                        orderId = args.orderId,
                        amount = params.totalAmount,
                    )
                )

            updateCheckout(checkout, response)
        } catch (e: ClientWebApplicationException) {
            checkout.status = Const.CheckoutStatus.PAYFAILED.value
            checkout.persist()
            loggingClientWebApplicationException(e, params.orderId)
            return Err(
                FanmeException(100, "Returned error response from GMO Payment with entryTran")
            )
        } catch (e: Exception) {
            checkout.status = Const.CheckoutStatus.PAYFAILED.value
            checkout.persist()
            loggingUnexpectedError(e, params.orderId)
            return Err(FanmeException(100, "Unexpected error occurred in entryTran"))
        }

        return Ok(Unit)
    }
}
