package jp.co.torihada.fanme.modules.payment.usecases.transfer

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.util.*
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.externals.client.gmo.ExtGmoTransferClient
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.EntryTransaction
import jp.co.torihada.fanme.modules.payment.externals.entity.transfer.LinkRedirectUrl
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import jp.co.torihada.fanme.modules.payment.services.ErrorResponse
import jp.co.torihada.fanme.modules.payment.services.Errors
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import org.jboss.resteasy.reactive.ClientWebApplicationException

@ApplicationScoped
class CreateTransfer {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject private lateinit var paymentConfig: PaymentConfig

    @Inject @RestClient private lateinit var extGmoTransferClient: ExtGmoTransferClient

    data class Input(val userId: String, val callbackUrl: String)

    data class Output(val authCode: String?, val gmoUrl: String?)

    fun generateShortUUID(): String {
        if (commonConfig.envKind() == "test") {
            return "testUUID"
        } else {
            val uuidBytes = ByteArray(16)
            UUID.randomUUID().let {
                val buffer = java.nio.ByteBuffer.wrap(uuidBytes)
                buffer.putLong(it.mostSignificantBits)
                buffer.putLong(it.leastSignificantBits)
            }
            return Base64.getUrlEncoder().withoutPadding().encodeToString(uuidBytes) // 22文字の短いUUID
        }
    }

    private fun getRandomString(length: Int): String {
        if (commonConfig.envKind() == "test") {
            return "testAuthCode"
        } else {
            val allowedChars = ('0'..'9')
            return (1..length).map { allowedChars.random() }.joinToString("")
        }
    }

    fun execute(params: Input): Result<Output, ErrorResponse> {

        // 現在申請中の振込情報を取得取得
        // 申請注のものがある場合はエラー
        val sellerGmoTransfersData =
            SellerGmoTransfer.findByStatusesAndSellerUserId(
                listOf(
                    Const.GmoTransferStatus.TransferRegistered.value,
                    Const.GmoTransferStatus.TransferDataRegistered.value,
                ),
                params.userId,
            )

        if (sellerGmoTransfersData.isNotEmpty()) {
            return Err(Errors.SELLER_GMO_TRANSFER_ALREADY_REGISTERED)
        }

        // 登録未完了のものがある場合はサイドURLを表示
        val sellerGmoTransferData =
            SellerGmoTransfer.findByStatusAndSellerUserId(
                Const.GmoTransferStatus.TransferNotRegistered.value,
                params.userId,
            )

        if (sellerGmoTransferData != null) {
            return Ok(
                Output(
                    authCode = sellerGmoTransferData.authCode,
                    gmoUrl = sellerGmoTransferData.gmoUrl,
                )
            )
        }

        // 出金作業開始
        val sellerAccountBalance =
            SellerAccountBalance.findBySellerUserId(params.userId)
                ?: return Err(Errors.SELLER_ACCOUNT_BALANCE_NOT_FOUND)

        val balance = sellerAccountBalance.amount

        val depositId = generateShortUUID()
        val transferAmount = if (balance!! >= 1_000_440) 1_000_000 else balance.minus(440)

        if (transferAmount <= 0) {
            return Err(Errors.SELLER_BALANCE_NOT_ENOUGH)
        }

        // GMOに対してのcallbackUrlの設定を行う
        val callbackUrl = params.callbackUrl

        val authCode = getRandomString(12)

        // sellerGmoTransfers テーブルにデータを入れる
        val sellerGmoTransfer =
            SellerGmoTransfer().apply {
                this.tenant = commonConfig.tenant()
                this.sellerUserId = params.userId
                this.depositId = depositId
                this.amount = transferAmount
                this.authCode = authCode
                this.status = Const.GmoTransferStatus.TransferNotRegistered.value
            }
        sellerGmoTransfer.persist()

        try {
            val response =
                extGmoTransferClient.linkRedirectUrl(
                    LinkRedirectUrl.Request(
                        shopId = paymentConfig.gmoTransferShopId(),
                        shopPass = paymentConfig.gmoTransferShopPass(),
                        depositId = depositId,
                        callBackUrl = callbackUrl,
                        amount = transferAmount,
                        authCode = authCode,
                        remitMethodBank = "1",
                        remitMethodSevenatm = "0",
                        remitMethodAmazongift = "0",
                        method = "1",
                        bankId = params.userId,
                    )
                )

            sellerGmoTransfer.gmoUrl = response.redirectUrl

            return Ok(Output(authCode = authCode, gmoUrl = response.redirectUrl))
        } catch (e: ClientWebApplicationException) {
            sellerGmoTransfer.status = Const.GmoTransferStatus.TransferDataFailed.value
            val errorResponseBody = e.response.readEntity(String::class.java)
            val objectMapper = ObjectMapper().registerKotlinModule()
            val errorResponses: List<EntryTransaction.ErrorResponse> =
                objectMapper.readValue(errorResponseBody)

            logger.error(
                "GMO transfer link redirect failed sellerGmoTransferId: ${sellerGmoTransfer.id}"
            )
            errorResponses.forEach { errorResponse ->
                val errorCode = errorResponse.errCode
                val errorInfo = errorResponse.errInfo
                logger.error(
                    "Failed to link redirect: ErrorCode: $errorCode, ErrorInfo: $errorInfo"
                )
            }

            return Err(Errors.GMO_DEPOSIT_REGISTRATION_FAILED)
        } catch (e: Exception) {
            sellerGmoTransfer.status = Const.GmoTransferStatus.TransferDataFailed.value
            logger.error(
                "GMO transfer link redirect failed sellerGmoTransferId: ${sellerGmoTransfer.id}"
            )
            return Err(Errors.GMO_DEPOSIT_REGISTRATION_FAILED)
        }
    }
}
