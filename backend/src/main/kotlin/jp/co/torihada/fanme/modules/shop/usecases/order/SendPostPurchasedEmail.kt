package jp.co.torihada.fanme.modules.shop.usecases.order

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const.RestrictedAccountType
import jp.co.torihada.fanme.modules.fanme.controllers.RestrictedAccountController
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.EmailService
import org.jboss.logging.Logger

@ApplicationScoped
class SendPostPurchasedEmail {
    @Inject private lateinit var restrictedAccountController: RestrictedAccountController
    @Inject private lateinit var emailService: EmailService
    @Inject private lateinit var logger: Logger

    data class Input(
        val order: Order,
        val purchasedItems: List<PurchasedItem>,
        val transaction: Transaction,
    )

    fun execute(params: Input) {
        try {
            emailService.sendPurchasedEmail(params.order, params.purchasedItems, params.transaction)
        } catch (e: Exception) {
            logger.error("Failed to send email to Purchaser", e)
        }
        try {
            val showSales =
                !restrictedAccountController.isRestricted(
                    RestrictedAccountType.HIDE_SALES,
                    params.order.shop.creatorUid,
                )
            if (showSales) {
                emailService.sendPurchasedSellerEmail(
                    params.order,
                    params.purchasedItems,
                    transaction = params.transaction,
                )
            }
            emailService.sendPurchasedSellerEmailForManager(
                params.order,
                params.purchasedItems,
                transaction = params.transaction,
            )
        } catch (e: Exception) {
            logger.error("Failed to send email to Seller", e)
        }
    }
}
