package jp.co.torihada.fanme.modules.shop.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.shop.Const.FILE_NAME_MAX_LENGTH

@PersistenceUnit(name = "sample")
@Entity
@Table(name = "samples")
class Sample : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "item_id", nullable = false, updatable = false)
    var item: Item = Item()

    @NotBlank
    @Size(max = FILE_NAME_MAX_LENGTH)
    @Column(nullable = false, length = FILE_NAME_MAX_LENGTH)
    var name: String = ""

    @NotBlank
    @Column(name = "object_uri", nullable = false, columnDefinition = "text")
    var objectUri: String = ""

    @Column(name = "thumbnail_uri", columnDefinition = "text") var thumbnailUri: String? = null

    @NotBlank
    @Column(name = "file_type", nullable = false, length = 10)
    var fileType: String = "any" // image, audio, video, any

    @Min(0) @Column(name = "size") var size: Float = 0.0f

    @Min(0)
    @Column(name = "duration")
    var duration: Int = 0 // audio or video duration in seconds. 0 means not audio or video.

    companion object : PanacheCompanion<Sample> {

        fun findByItemId(itemId: Long): List<Sample> {
            return find("item.id = ?1", itemId).list()
        }

        fun create(
            itemId: Long,
            name: String,
            objectUri: String,
            thumbnailUri: String?,
            fileType: String,
            size: Float,
            duration: Int?,
        ): Sample {
            val sample = Sample()
            sample.item = Item.findById(itemId)!!
            sample.name = name
            sample.objectUri = objectUri
            sample.thumbnailUri = thumbnailUri
            sample.fileType = fileType
            sample.size = size
            sample.duration = duration ?: 0
            sample.persist()
            return sample
        }

        fun update(
            id: Long,
            name: String,
            objectUri: String,
            thumbnailUri: String?,
            fileType: String,
            size: Float,
            duration: Int?,
        ): Sample {
            val sample = findById(id)!!
            sample.name = name
            sample.objectUri = objectUri
            sample.thumbnailUri = thumbnailUri
            sample.fileType = fileType
            sample.size = size
            sample.duration = duration ?: 0
            sample.persist()
            return sample
        }

        fun delete(itemId: Long) {
            val samples = findByItemId(itemId)
            samples.forEach { it.delete() }
        }

        /** IDが指定されていれば更新、なければ新規作成を行うupsert操作 */
        fun upsert(
            itemId: Long,
            id: Long?,
            name: String,
            objectUri: String,
            thumbnailUri: String?,
            fileType: String,
            size: Float,
            duration: Int?,
        ): Sample {
            return if (id != null && findById(id) != null) {
                // 既存レコードの更新
                update(id, name, objectUri, thumbnailUri, fileType, size, duration)
            } else {
                // 新規レコードの作成
                create(itemId, name, objectUri, thumbnailUri, fileType, size, duration)
            }
        }
    }
}
