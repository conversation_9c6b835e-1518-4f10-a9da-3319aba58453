package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class GetAgencyUsers {

    @Inject private lateinit var securityUtils: SecurityUtils

    data class Input(
        val agencyId: Long,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    fun execute(input: Input): Result<List<User>, Exception> {
        val agency = Agency.findById(input.agencyId)
        if (agency == null || agency.deletedAt != null) {
            throw ResourceNotFoundException("Agency")
        }

        if (input.currentUserRole == ConsoleUserRole.AGENT) {
            val currentConsoleUser = ConsoleUser.findByUserUid(input.currentUserUid)
            securityUtils.validateAgentAccess(
                input.agencyId,
                input.currentUserRole.value,
                currentConsoleUser?.agencyId,
            )
        }

        val consoleUsers = ConsoleUser.findByAgencyIdAndRole(input.agencyId, UserRole.CREATOR_VALUE)
        val users = consoleUsers.map { it.user }

        return Ok(users)
    }
}
