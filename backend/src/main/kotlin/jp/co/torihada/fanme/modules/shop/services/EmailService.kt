package jp.co.torihada.fanme.modules.shop.services

import io.quarkus.mailer.Mail
import io.quarkus.mailer.Mailer
import jakarta.enterprise.context.ApplicationScoped
import jakarta.enterprise.inject.spi.CDI
import jakarta.inject.Inject
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.*
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.externals.client.auth.FanmeAuthClient
import jp.co.torihada.fanme.externals.lib.FanmeAuthAuthorizer
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.annotations.Maskable
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.utils.MaskingProcessor
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

@ApplicationScoped
class EmailService {

    @Inject private lateinit var logger: Logger

    @Inject private lateinit var config: ShopConfig
    @Inject private lateinit var mailer: Mailer

    @Inject private lateinit var userController: UserController
    @Inject private lateinit var authorizer: FanmeAuthAuthorizer
    @Inject @RestClient private lateinit var authClient: FanmeAuthClient

    data class Form(
        val toAddress: String,
        val toName: String? = "",
        val subject: String,
        val text: String,
    )

    data class ItemDetail(@Maskable val name: String, val id: Long?, val price: Int)

    private lateinit var cvsPaymentReminderText: String
    private lateinit var cvsPaymentReminderFamilyMartText: String
    private lateinit var purchasedMessageText: String
    private lateinit var purchasedGachaMessageText: String
    private lateinit var purchasedSellerMessageText: String
    private lateinit var receivingApplicationText: String
    private lateinit var transferDeadLineContactText: String

    init {
        val _config = CDI.current().select(CommonConfig::class.java).get()
        cvsPaymentReminderText = loadTemplate(_config.tenant(), "cvs_payment_reminder.txt")
        cvsPaymentReminderFamilyMartText =
            loadTemplate(_config.tenant(), "cvs_payment_reminder_familymart.txt")
        purchasedMessageText = loadTemplate(_config.tenant(), "purchased.txt")
        purchasedGachaMessageText = loadTemplate(_config.tenant(), "purchased_each.txt")
        purchasedSellerMessageText = loadTemplate(_config.tenant(), "purchased_seller.txt")
        receivingApplicationText = loadTemplate(_config.tenant(), "receiving_application.txt")
        transferDeadLineContactText =
            loadTemplate(_config.tenant(), "transfer_deadline_contact.txt")
    }

    fun sendPurchasedEmail(
        order: Order,
        purchasedItems: List<PurchasedItem>,
        transaction: Transaction,
    ) {
        val resources = getPurchaseEmailResources(order, transaction, "purchaser")
        if (resources.authUser.email == null) {
            logger.info("not send email because purchaser email is null")
            return
        }

        val name = resources.purchaser.name ?: "FANMEユーザー 様"

        val itemDetails =
            purchasedItems.joinToString("\n\n") {
                createItemDetail(resources.seller.accountIdentity!!, it)
            }
        val tip =
            if (resources.transaction.tip != null) {
                "チップ　　　：${formatAmount(resources.transaction.tip?.amount!!)}円\n"
            } else {
                ""
            }
        val fee =
            if (resources.checkout.cvsFee != null) {
                "事務手数料　　　：${formatAmount(resources.checkout.cvsFee!!)}円\n"
            } else {
                ""
            }

        val orderDetails = itemDetails + "\n" + tip + "\n" + fee
        val orderNumber = Order.generateOrderNumber(order.id!!)

        val paymentDetail =
            """
            商品合計(チップ含む)：${formatAmount(resources.transaction.amount!!)}円(税込)
            送料　　  　　　　　：${formatAmount(resources.checkout.deliveryFee?:0)}円(税込)
            事務手数料　　　　  ：${formatAmount(resources.checkout.cvsFee?:0)}円(税込)
            お支払い合計  　　　：${formatAmount(resources.transaction.totalAmount!!)}円(税込)
            お支払方法　  　　　：${Const.PaymentType.fromValue(resources.transaction.paymentType!!)?.displayName}
        """
                .trimIndent()

        val emailText = purchasedMessageText.format(name, orderNumber, orderDetails, paymentDetail)
        sendPlainTextEmail(
            Form(toAddress = resources.authUser.email, subject = "ご購入ありがとうございます", text = emailText)
        )
    }

    fun sendPurchasedSellerEmail(
        order: Order,
        purchasedItems: List<PurchasedItem>,
        transaction: Transaction,
    ) {
        val resources = getPurchaseEmailResources(order, transaction, "seller")
        if (resources.authUser.email == null) {
            logger.info("not send email because seller email is null")
            return
        }

        val emailText = generatePurchasedSellerEmail(purchasedItems, resources)

        sendPlainTextEmail(
            Form(toAddress = resources.authUser.email, subject = "商品が購入されました", text = emailText)
        )
    }

    // 該当クリエイターを管理している事務所マネージャーへの同じメールを転送する
    fun sendPurchasedSellerEmailForManager(
        order: Order,
        purchasedItems: List<PurchasedItem>,
        transaction: Transaction,
    ) {
        val resources = getPurchaseEmailResources(order, transaction, "seller")
        val managerEmails = agencyManagerEmails[resources.seller.uid]
        if (managerEmails.isNullOrEmpty()) return

        val emailText = generatePurchasedSellerEmail(purchasedItems, resources)

        agencyManagerEmails[resources.seller.uid]?.forEach {
            sendPlainTextEmail(Form(toAddress = it, subject = "商品が購入されました", text = emailText))
        }
    }

    private fun generatePurchasedSellerEmail(
        purchasedItems: List<PurchasedItem>,
        resources: EmailService.PurchaseEmailResources,
    ): String {
        val name = resources.seller.name ?: "FANMEユーザー 様"
        val itemDetails =
            purchasedItems.joinToString("\n\n") {
                createItemDetail(resources.seller.accountIdentity!!, it, false)
            }
        val tip =
            if (resources.transaction.tip != null) {
                "チップ　　　：${formatAmount(resources.transaction.tip?.amount!!)}円\n"
            } else {
                ""
            }
        val orderDetails = itemDetails + "\n" + tip + "\n"
        val totalAmount = formatAmount(resources.transaction.totalAmount!!) + "円"

        val emailText =
            purchasedSellerMessageText.format(
                name,
                resources.purchaser.name,
                resources.purchaser.accountIdentity,
                orderDetails,
                totalAmount,
            )
        return emailText
    }

    private fun createItemDetail(
        creatorAccountIdentity: String,
        purchasedItem: PurchasedItem,
        toPurchaser: Boolean = true,
    ): String {
        val (price, originalItemName) = getItemPriceAndName(purchasedItem)
        val totalPrice = BigDecimal(price).multiply(BigDecimal(purchasedItem.quantity)).toInt()
        val itemDetail =
            ItemDetail(name = originalItemName, id = purchasedItem.item.id, price = price)
        val maskedItemDetail = MaskingProcessor.process(itemDetail)
        val itemName = maskedItemDetail.name
        val priceText = formatAmount(price) + "円"
        val totalPriceText = formatAmount(totalPrice) + "円"

        if (toPurchaser) {
            val viewerUrl =
                "${config.shopFrontUrl()}/@${creatorAccountIdentity}/viewer/${purchasedItem.item.id}"
            return """
                商品名　　：${itemName}
                商品ページ：${viewerUrl}
                税込単価　：${priceText}
                数量　　　：${purchasedItem.quantity}
                小計　　　：${totalPriceText}
                """
                .trimIndent()
        } else {
            return """
                商品名　　：${itemName}
                税込単価　：${priceText}
                数量　　　：${purchasedItem.quantity}
                小計　　　：${totalPriceText}
                """
                .trimIndent()
        }
    }

    private fun getItemPriceAndName(purchasedItem: PurchasedItem): Pair<Int, String> {
        val isSingleFile = purchasedItem.itemFile != null
        val price = purchasedItem.price

        return if (isSingleFile) {
            Pair(price, purchasedItem.itemFile?.name ?: purchasedItem.item.name)
        } else {
            Pair(price, purchasedItem.item.name)
        }
    }

    fun sendTransferDeadlineContactEmail(sellerEmail: String, sellerName: String?) {
        val name = if (sellerName.isNullOrBlank()) "" else "$sellerName 様\n"

        sendPlainTextEmail(
            Form(
                toAddress = sellerEmail,
                subject = "【重要】売上金受け取り申請のお願い",
                text = transferDeadLineContactText.format(name),
            )
        )
    }

    fun sendCvsPaymentReminderMail(
        purchaserEmail: String,
        convenience: String,
        cvsPaymentDeadlineYear: String,
        cvsPaymentDeadlineMonth: String,
        cvsPaymentDeadlineDay: String,
        receiptNo: String,
        confNo: String,
        purchaserName: String?,
    ) {
        val name = if (purchaserName.isNullOrBlank()) "FANMEユーザー 様" else "$purchaserName 様\n"
        val emailText =
            if (convenience == "ファミリーマート") {
                cvsPaymentReminderFamilyMartText.format(
                    name,
                    convenience,
                    cvsPaymentDeadlineYear,
                    cvsPaymentDeadlineMonth,
                    cvsPaymentDeadlineDay,
                    confNo,
                    receiptNo,
                )
            } else {
                cvsPaymentReminderText.format(
                    name,
                    convenience,
                    cvsPaymentDeadlineYear,
                    cvsPaymentDeadlineMonth,
                    cvsPaymentDeadlineDay,
                    receiptNo,
                    confNo,
                )
            }

        return sendPlainTextEmail(
            Form(toAddress = purchaserEmail, subject = "コンビニ決済未完了のお知らせ", text = emailText)
        )
    }

    private fun loadTemplate(tenant: String, name: String): String {
        return javaClass.classLoader
            .getResource("messages/${tenant}_${name}")
            ?.readText(Charsets.UTF_8) ?: ""
    }

    private fun sendPlainTextEmail(form: Form) {
        val mail = Mail.withText(form.toAddress, form.subject, form.text).apply {}
        mailer.send(mail)
    }

    @Suppress("UnusedPrivateMember")
    private fun sendHtmlEmail(form: Form) {
        val mail = Mail.withHtml(form.toAddress, form.subject, form.text).apply {}
        mailer.send(mail)
    }

    private fun formatAmount(amount: Int): String {
        val numberFormat = NumberFormat.getCurrencyInstance(Locale.JAPAN).format(amount)
        return numberFormat.replace("￥", "")
    }

    data class PurchaseEmailResources(
        val purchaser: User,
        val seller: User,
        val authUser: FanmeAuthClient.AuthUser,
        val transaction: Transaction,
        val checkout: Checkout,
    )

    private fun getPurchaseEmailResources(
        order: Order,
        transaction: Transaction,
        recipient: String,
    ): PurchaseEmailResources {
        val purchaser = userController.getUser(order.purchaserUid) as? User
        val seller = userController.getUser(order.shop.creatorUid!!) as? User
        val authorization = authorizer.getAuthorization()
        val authUser =
            if (recipient == "purchaser") {
                authClient.getAuthUser(purchaser?.uid!!, authorization)
            } else {
                authClient.getAuthUser(seller?.uid!!, authorization)
            }
        val checkout = transaction.checkout
        return PurchaseEmailResources(
            purchaser = purchaser!!,
            seller = seller!!,
            authUser = authUser,
            transaction = transaction,
            checkout = checkout!!,
        )
    }
}
