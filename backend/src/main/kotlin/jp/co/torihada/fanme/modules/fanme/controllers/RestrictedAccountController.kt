package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.usecases.restrectedAccount.IsRestrictedAccounts

@ApplicationScoped
class RestrictedAccountController : BaseController() {

    @Inject private lateinit var isRestricted: IsRestrictedAccounts

    fun isRestricted(type: Int, userUuid: String?): Bo<PERSON>an {
        if (userUuid == null) return false
        return isRestricted.execute(IsRestrictedAccounts.Input(userUuid, type)).getOrElse { false }
    }
}
