package jp.co.torihada.fanme.modules.payment.usecases.payments.webhook

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.Const.SUCCEED_PAYMENT_LOG_FORMAT
import jp.co.torihada.fanme.modules.payment.externals.BaseExternals
import jp.co.torihada.fanme.modules.payment.externals.client.payment.ExtPaymentClient
import jp.co.torihada.fanme.modules.payment.externals.client.shop.ExtShopClient
import jp.co.torihada.fanme.modules.payment.externals.entity.payment.MiniappWebhook
import jp.co.torihada.fanme.modules.payment.externals.entity.shop.UpdateOrder
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.services.ErrorResponse
import jp.co.torihada.fanme.modules.payment.services.Errors
import jp.co.torihada.fanme.modules.payment.usecases.payments.common.BaseExecTransaction
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

@ApplicationScoped
class WebhookPayment : BaseExecTransaction() {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var commonConfig: CommonConfig
    @Inject @RestClient private lateinit var extShopClient: ExtShopClient
    @Inject @RestClient private lateinit var extPaymentClient: ExtPaymentClient
    @Inject private lateinit var baseExternals: BaseExternals

    data class Input(
        val shopId: String,
        val shopPass: String,
        val accessId: String,
        val accessPass: String,
        val orderId: String,
        val status: String,
        val amount: String,
        val tax: String,
        val payType: String,
    )

    class Output(val transactionId: Long? = null)

    fun execute(params: Input): Result<Output, ErrorResponse> {

        try {
            var status = params.status
            // accessId. orderIdが一致するかpending transactionを確認
            val checkout =
                Checkout.findByAccessIdAndOrderId(params.accessId, params.orderId)
                    ?: return Err(Errors.GMO_SHOP_DATA_FAILED)

            if (!checkout.isShop) {
                try {
                    val response =
                        extPaymentClient.miniappWebhook(
                            authorization = baseExternals.getPaymentAuthorization(),
                            MiniappWebhook.Request(
                                shopId = params.shopId,
                                shopPass = params.shopPass,
                                accessId = params.accessId,
                                accessPass = params.accessPass,
                                orderId = params.orderId,
                                status = params.status,
                                amount = params.amount,
                                tax = params.tax,
                                payType = params.payType,
                            ),
                        )

                    val responseBody = response.readEntity(String::class.java)
                    if (responseBody != "0") {
                        return Err(Errors.MINIAPP_WEBHOOK_FAILED)
                    }
                    logger.info(
                        SUCCEED_PAYMENT_LOG_FORMAT.format(
                            checkout.type,
                            checkout.orderId,
                            checkout.id,
                        )
                    )
                    return Ok(Output())
                } catch (e: Exception) {
                    logger.error(
                        "Failed to miniappWebhook: unexpected error occurred. OrderId: ${params.orderId}, message: ${e.message}",
                        e,
                    )
                    return Err(Errors.MINIAPP_WEBHOOK_FAILED)
                }
            }

            // すでに完了しているものは何もしない
            // ここを使用すればキャッシュバックも対応できそう
            if (checkout.status == Const.CheckoutStatus.PAYSUCCESS.value) {
                return Ok(Output())
            }

            // PayPayの場合は、statusがCAPTUREの場合はPAYSUCCESSに変更
            if (checkout.type == PaymentConst.PaymentType.PAY_PAY.value && status == "CAPTURE") {
                status = Const.CheckoutStatus.PAYSUCCESS.value
            }
            // checkoutsのstatusを更新
            when (status) {
                "UNPROCESSED" -> checkout.status = Const.CheckoutStatus.UNPROCESSED.value
                "REQSUCCESS" -> checkout.status = Const.CheckoutStatus.REQSUCCESS.value
                "PAYSUCCESS" -> checkout.status = Const.CheckoutStatus.PAYSUCCESS.value
                "EXPIRED" -> checkout.status = Const.CheckoutStatus.EXPIRED.value
                "CANCEL" -> checkout.status = Const.CheckoutStatus.CANCEL.value
                "PAYFAIL" -> checkout.status = Const.CheckoutStatus.PAYFAILED.value
            }

            // UNPROCESSED、REQSUCCESSの場合は、データの変更なし
            if (
                checkout.status == Const.CheckoutStatus.UNPROCESSED.value ||
                    checkout.status == Const.CheckoutStatus.REQSUCCESS.value
            ) {
                return Ok(Output())
            }

            // PAYSUCCESSの場合はTransactionsを作成
            var transaction = Transaction()
            if (checkout.status == Const.CheckoutStatus.PAYSUCCESS.value) {
                // Transactionsを作成
                transaction =
                    Transaction().apply {
                        this.tenant = commonConfig.tenant()
                        this.orderId = checkout.orderId
                        this.orderedAt = checkout.createdAt
                        this.sellerUserId = checkout.sellerUserId
                        this.purchaserUserId = checkout.purchaserUserId
                        this.checkout = checkout
                        this.tip = checkout.tip
                        this.amount = checkout.amount
                        this.totalAmount = checkout.total
                        this.status = PaymentConst.TransactionStatus.Success.value
                        this.paymentType = checkout.type
                    }
                transaction.persist()

                // クリエイターとテナンtの売上を登録
                saveSellerAndTenantRevenue(transaction, checkout)
            }

            // shopのorderを更新
            if (checkout.status != null) {
                try {
                    val authorization = baseExternals.getShopAuthorization()
                    extShopClient.updateOrder(
                        authorization = authorization,
                        UpdateOrder.Request(
                            transactionId = transaction.id,
                            checkoutId = checkout.id!!,
                            status = checkout.status!!,
                        ),
                    )
                    logger.info(
                        SUCCEED_PAYMENT_LOG_FORMAT.format(
                            checkout.type,
                            checkout.orderId,
                            checkout.id,
                        )
                    )
                    return Ok(Output(transactionId = transaction.id))
                } catch (e: Exception) {
                    logger.error(
                        "Failed to updateOrder for WebhookPayment: unexpected error occurred. OrderId: ${checkout.orderId}, message: ${e.message}",
                        e,
                    )
                    throw RuntimeException(
                        "Transaction rollback triggered due to external client failure"
                    )
                }
            } else {
                logger.error("orderId or status is null")
                throw RuntimeException(
                    "Transaction rollback triggered due to external client failure"
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to updateOrder for WebhookPayment", e)
            throw RuntimeException("Transaction rollback triggered due to external client failure")
        }
    }
}
