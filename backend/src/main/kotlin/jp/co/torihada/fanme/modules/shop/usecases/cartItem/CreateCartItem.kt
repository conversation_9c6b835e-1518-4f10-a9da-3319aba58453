package jp.co.torihada.fanme.modules.shop.usecases.cartItem

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.exception.*
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.*

@ApplicationScoped
class CreateCartItem {
    data class Input(
        val purchaserUid: String,
        val creatorUid: String,
        val itemId: Long,
        val singleFile: Long?,
        val quantity: Int,
    )

    @Transactional
    fun execute(params: Input): Result<CartItem, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))
        val cart =
            Cart.findByUserUidAndShopId(params.purchaserUid, shop.id!!)
                ?: Cart.create(params.purchaserUid, shop.id!!)
        val item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        val cartItems = cart.items

        // 購入可能数のチェック
        if (params.singleFile == null) {
            checkPurchasableLimit(params, cartItems, item).getOrElse {
                return Err(it)
            }
        }

        if (item.itemType == ItemType.CHEKI) {
            val existingCartItem = CartItem.findByForeignKey(cart.id!!, params.itemId, null)
            // 商品タイプがチェキ、かつ既存のカートアイテムが存在する場合、数量を更新
            if (existingCartItem != null && item.itemType == ItemType.CHEKI) {
                return addDuplicatedCartItem(existingCartItem, params)
            }
        } else {
            checkPurchaseStatus(params).getOrElse {
                return Err(it)
            }
            checkCheckoutStatus(params).getOrElse {
                return Err(it)
            }
            checkCartConflicts(cartItems, params).getOrElse {
                return Err(it)
            }
        }

        return Ok(CartItem.create(cart.id!!, params.itemId, params.quantity, params.singleFile))
    }

    private fun addDuplicatedCartItem(
        cartItem: CartItem,
        params: Input,
    ): Result<CartItem, FanmeException> {
        val newQuantity = cartItem.quantity + params.quantity
        val updated = CartItem.update(cartItem.id!!, newQuantity, null)
        return Ok(updated)
    }

    private fun checkPurchaseStatus(params: Input): Result<Boolean, FanmeException> {
        // セット商品の購入状態チェック
        val isPurchased = PurchasedItem.isPurchased(params.itemId, params.purchaserUid, null)
        if (isPurchased) {
            return Err(CartSetItemAlreadyPurchasedException())
        }

        val isTargetSinglePurchased =
            PurchasedItem.isPurchased(params.itemId, params.purchaserUid, params.singleFile)
        // 単品商品の購入状態チェック
        if (params.singleFile != null && isTargetSinglePurchased) {
            return Err(CartSingleItemAlreadyPurchasedException())
        }
        return Ok(true)
    }

    private fun checkCheckoutStatus(params: Input): Result<Boolean, FanmeException> {
        // セット商品の申請状態チェック
        val isCheckout = PurchasedItem.isCheckout(params.itemId, params.purchaserUid, null)
        if (isCheckout) {
            return Err(CartSetItemAlreadyCheckoutException())
        }

        // 単品商品の申請状態チェック
        val isTargetSingleCheckout =
            PurchasedItem.isCheckout(params.itemId, params.purchaserUid, params.singleFile)
        if (params.singleFile != null && isTargetSingleCheckout) {
            return Err(CartSingleItemAlreadyCheckoutException())
        }
        return Ok(true)
    }

    private fun checkCartConflicts(
        cartItems: MutableSet<CartItem>,
        params: Input,
    ): Result<Boolean, FanmeException> {
        if (params.singleFile != null) {
            // 単品商品追加時のセット商品との競合チェック
            if (cartItems.any { params.itemId == it.item.id && it.singleFile == null }) {
                return Err(CartSingleItemWithSetException())
            }
        } else {
            // セット商品追加時の単品商品との競合チェック
            if (cartItems.any { params.itemId == it.item.id && it.singleFile != null }) {
                return Err(CartSetItemWithSingleException())
            }
        }
        return Ok(true)
    }

    private fun checkPurchasableLimit(
        params: Input,
        cartItems: MutableSet<CartItem>,
        item: Item,
    ): Result<Boolean, FanmeException> {
        val purchasableCount = Util.getPurchasableCount(item, params.purchaserUid)
        val addedCount = cartItems.filter { it.item.id == params.itemId }.sumOf { it.quantity }

        return if (addedCount + params.quantity <= purchasableCount) {
            Ok(true)
        } else {
            Err(CartSetItemOverPurchasableLimitException())
        }
    }
}
