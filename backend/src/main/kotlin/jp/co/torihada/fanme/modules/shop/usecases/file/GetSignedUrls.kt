package jp.co.torihada.fanme.modules.shop.usecases.file

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.services.aws.S3

@ApplicationScoped
class GetPreSignedUrls {

    @Inject lateinit var s3: S3

    data class Input(val shopId: Long, val metadataList: List<PreSignedFileMetadata>)

    data class PreSignedFileMetadata(val id: String, val key: String)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class PreSignedUrl(val id: String, val key: String, val url: String)

    fun execute(params: Input): Result<List<PreSignedUrl>, FanmeException> {
        return try {
            val preSignedUrls =
                params.metadataList.map {
                    PreSignedUrl(
                        id = it.id,
                        key = it.key,
                        url =
                            s3.getPresignedUrl(
                                "${params.shopId}/${it.key}",
                                Util.PresignedUrlType.GET_OBJECT,
                                null,
                            ),
                    )
                }
            Ok(preSignedUrls)
        } catch (e: FanmeException) {
            Err(e)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }
}
