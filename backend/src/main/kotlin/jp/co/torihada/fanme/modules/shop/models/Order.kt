package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.Const as CommonConst
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.Util
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.odata.OData

@PersistenceUnit(name = "order")
@Entity
@Table(name = "orders")
class Order : BaseModel() {

    @NotBlank
    @Column(name = "purchaser_uid", length = USER_UID_MAX_LENGTH)
    var purchaserUid: String = ""

    @NotNull
    @ManyToOne
    @JoinColumn(name = "shop_id", nullable = false, updatable = false)
    var shop: Shop = Shop()

    @Column(name = "transaction_id") var transactionId: Long? = null

    @Column(name = "checkout_id") var checkoutId: Long? = null

    @OneToMany(mappedBy = "order", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var purchasedItems: MutableSet<PurchasedItem> = mutableSetOf()

    @OneToMany(mappedBy = "order", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var orderItems: MutableSet<OrderItem> = mutableSetOf()

    companion object : PanacheCompanion<Order> {
        fun findByPurchaserUid(purchaserUid: String, top: Int?, skip: Int?): List<Order> {
            val find = find("purchaser_uid", purchaserUid)
            return Util.getEntityListWithPagination(find, top, skip) as List<Order>
        }

        fun create(
            purchaserUid: String,
            shopId: Long,
            transactionId: Long?,
            checkoutId: Long?,
        ): Order {
            val order = Order()
            order.purchaserUid = purchaserUid
            order.shop = Shop.findById(shopId)!!
            order.transactionId = transactionId
            order.checkoutId = checkoutId
            order.persist()
            return order
        }

        fun findByCheckoutId(checkoutId: Long): Order? {
            return find("checkoutId", checkoutId).firstResult()
        }

        fun findByTransactionId(transactionId: Long): Order? {
            return find("transactionId", transactionId).firstResult()
        }

        fun updateOrder(checkoutId: Long, transactionId: Long?): Order? {
            val order = find("checkoutId", checkoutId).firstResult()
            if (transactionId != null)
                if (order != null) {
                    order.transactionId = transactionId
                    order.checkoutId = checkoutId
                    order.persist()
                }
            return order
        }

        fun findSucceedOrdersByShopId(shopId: Long, odata: OData?): List<Order> {
            val status = Const.CheckoutStatus.PAYSUCCESS.value
            val find =
                find(
                    "select o from Order o inner join PurchasedItem p on o.id = p.order.id where p.status = ?1 and o.shop.id = ?2 group by o.id order by o.updatedAt desc",
                    status,
                    shopId,
                )
            return Util.getEntityListWithPagination(find, odata?.top ?: 10, odata?.skip ?: 0)
                as List<Order>
        }

        fun generateOrderNumber(orderId: Long): String {
            return "${CommonConst.Order.NUMBER_PREFIX}${orderId.toString().padStart(CommonConst.Order.NUMBER_LENGTH, '0')}"
        }
    }
}
