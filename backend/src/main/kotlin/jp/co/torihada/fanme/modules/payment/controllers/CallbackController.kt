package jp.co.torihada.fanme.modules.payment.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.constraints.NotBlank
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.Const.SUCCEED_PAYMENT_LOG_FORMAT
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.usecases.callback.PayPayCallback
import jp.co.torihada.fanme.modules.payment.usecases.payments.card.*
import jp.co.torihada.fanme.modules.shop.Util.Gmo3DSecureCallbackEventName
import jp.co.torihada.fanme.modules.shop.Util.GmoTds2TransResult
import jp.co.torihada.fanme.modules.shop.Util.GmoTds2TransResult.*
import org.jboss.logging.Logger

@ApplicationScoped
class CallbackController {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var payPayCallback: PayPayCallback

    @Inject private lateinit var tds2Result: Tds2Result

    @Inject private lateinit var tds2Auth: Tds2Auth

    @Inject private lateinit var secureTran2: SecureTran2

    @Inject private lateinit var createTransactionByCheckout: CreateTransactionByCheckout

    @Inject private lateinit var postCreditCard3DSecureProcessor: PostCreditCard3DSecureProcessor

    @Inject
    private lateinit var postCreditCard3DSecureSingleOrderProcessor:
        PostCreditCard3DSecureSingleOrderProcessor

    data class HandleTds2transResultOutput(
        val isSuccess: Boolean,
        val challengeUrl: String? = null,
    )

    data class CreditCard3DSecureCallbackOutput(
        val returnUrl: String,
        val challengeUrl: String? = null,
    )

    @Transactional
    fun payPayCallback(
        @NotBlank shopId: String,
        @NotBlank orderId: String,
        @NotBlank status: String,
        @NotBlank tranDate: String,
        @NotBlank checkString: String,
        payPayTrackingId: String?,
        errorCode: String?,
        errorInfo: String?,
    ): PayPayCallback.Output {
        return payPayCallback
            .execute(
                PayPayCallback.Input(
                    shopId = shopId,
                    orderId = orderId,
                    status = status,
                    tranDate = tranDate,
                    payPayTrackingId = payPayTrackingId,
                    checkString = checkString,
                    errorCode = errorCode,
                    errorInfo = errorInfo,
                )
            )
            .getOrElse { throw it }
    }

    fun creditCard3DSecureCallback(
        @NotBlank md: String,
        @NotBlank event: String,
        @NotBlank param: String,
    ): CreditCard3DSecureCallbackOutput {
        val checkout = Checkout.findByAccessId(md) ?: throw ResourceNotFoundException("Checkout")
        val metadata = Checkout.getMetadata(checkout.orderId!!)
        val gmo3DSecureCallbackEvent = Gmo3DSecureCallbackEventName.fromValue(event)

        try {
            if (
                arrayOf(
                        Gmo3DSecureCallbackEventName.EVENT_3DS_METHOD_FINISHED,
                        Gmo3DSecureCallbackEventName.EVENT_3DS_METHOD_SKIPPED,
                    )
                    .contains(gmo3DSecureCallbackEvent)
            ) {
                // redirectUrlでGMO側で処理した後のコールバックの場合

                // 認証実行APIを実行
                val tds2AuthOutput =
                    tds2Auth
                        .execute(
                            Tds2Auth.Input(
                                accessId = checkout.accessId!!,
                                accessPass = checkout.accessPass!!,
                                tds2Param = param,
                            )
                        )
                        .getOrElse { throw it }

                val result =
                    handleTds2transResult(
                        gmoTds2AuthResult = tds2AuthOutput.gmoTds2TransResult,
                        checkout = checkout,
                        challengeUrl = tds2AuthOutput.challengeUrl,
                    )
                return if (result.isSuccess) {
                    logger.info(
                        SUCCEED_PAYMENT_LOG_FORMAT.format(
                            checkout.type,
                            checkout.orderId,
                            checkout.id,
                        )
                    )
                    CreditCard3DSecureCallbackOutput(metadata.successUrl!!, result.challengeUrl)
                } else {
                    CreditCard3DSecureCallbackOutput(metadata.errorUrl!!, result.challengeUrl)
                }
            } else {
                // challengeUrlでGMO側で処理した後のコールバックの場合

                // 認証結果取得APIを実行
                val tds2ResultOutput =
                    tds2Result.execute(
                        Tds2Result.Input(
                            accessId = checkout.accessId!!,
                            accessPass = checkout.accessPass!!,
                        )
                    )

                val result =
                    handleTds2transResult(
                        gmoTds2AuthResult = tds2ResultOutput.value.gmoTds2TransResult,
                        checkout = checkout,
                        challengeUrl = null,
                    )
                return if (result.isSuccess) {
                    logger.info(
                        SUCCEED_PAYMENT_LOG_FORMAT.format(
                            checkout.type,
                            checkout.orderId,
                            checkout.id,
                        )
                    )
                    CreditCard3DSecureCallbackOutput(metadata.successUrl!!)
                } else {
                    CreditCard3DSecureCallbackOutput(metadata.errorUrl!!)
                }
            }
        } catch (e: Exception) {
            logger.error(
                "[creditCard3DSecureCallback] Unexpected error occurred. accessId: $md, message: ${e.message}",
                e,
            )
            return CreditCard3DSecureCallbackOutput(metadata.errorUrl!!, null)
        }
    }

    // single order用のコールバック
    fun creditCard3DSecureCallbackSingleOrder(
        @NotBlank md: String,
        @NotBlank event: String,
        @NotBlank param: String,
    ): CreditCard3DSecureCallbackOutput {
        val checkout = Checkout.findByAccessId(md) ?: throw ResourceNotFoundException("Checkout")
        val metadata = Checkout.getMetadata(checkout.orderId!!)
        val gmo3DSecureCallbackEvent = Gmo3DSecureCallbackEventName.fromValue(event)

        try {
            if (
                arrayOf(
                        Gmo3DSecureCallbackEventName.EVENT_3DS_METHOD_FINISHED,
                        Gmo3DSecureCallbackEventName.EVENT_3DS_METHOD_SKIPPED,
                    )
                    .contains(gmo3DSecureCallbackEvent)
            ) {
                // redirectUrlでGMO側で処理した後のコールバックの場合

                // 認証実行APIを実行
                val tds2AuthOutput =
                    tds2Auth
                        .execute(
                            Tds2Auth.Input(
                                accessId = checkout.accessId!!,
                                accessPass = checkout.accessPass!!,
                                tds2Param = param,
                            )
                        )
                        .getOrElse { throw it }

                val result =
                    handleTds2transSingleOrderResult(
                        gmoTds2AuthResult = tds2AuthOutput.gmoTds2TransResult,
                        checkout = checkout,
                        challengeUrl = tds2AuthOutput.challengeUrl,
                    )

                return if (result.isSuccess) {
                    logger.info(
                        SUCCEED_PAYMENT_LOG_FORMAT.format(
                            checkout.type,
                            checkout.orderId,
                            checkout.id,
                        )
                    )
                    CreditCard3DSecureCallbackOutput(metadata.successUrl!!, result.challengeUrl)
                } else {
                    CreditCard3DSecureCallbackOutput(metadata.errorUrl!!, result.challengeUrl)
                }
            } else {
                // challengeUrlでGMO側で処理した後のコールバックの場合

                // 認証結果取得APIを実行
                val tds2ResultOutput =
                    tds2Result.execute(
                        Tds2Result.Input(
                            accessId = checkout.accessId!!,
                            accessPass = checkout.accessPass!!,
                        )
                    )

                val result =
                    handleTds2transSingleOrderResult(
                        gmoTds2AuthResult = tds2ResultOutput.value.gmoTds2TransResult,
                        checkout = checkout,
                        challengeUrl = null,
                    )
                return if (result.isSuccess) {
                    logger.info(
                        SUCCEED_PAYMENT_LOG_FORMAT.format(
                            checkout.type,
                            checkout.orderId,
                            checkout.id,
                        )
                    )
                    CreditCard3DSecureCallbackOutput(metadata.successUrl!!)
                } else {
                    CreditCard3DSecureCallbackOutput(metadata.errorUrl!!)
                }
            }
        } catch (e: Exception) {
            logger.error(
                "[creditCard3DSecureCallback] Unexpected error occurred. accessId: $md, message: ${e.message}",
                e,
            )
            return CreditCard3DSecureCallbackOutput(metadata.errorUrl!!, null)
        }
    }

    private fun handleTds2transResult(
        gmoTds2AuthResult: GmoTds2TransResult,
        checkout: Checkout,
        challengeUrl: String?,
    ): HandleTds2transResultOutput {
        when (gmoTds2AuthResult) {
            SUCCESS,
            ATTEMPTED -> {
                secureTran2
                    .execute(
                        SecureTran2.Input(
                            accessId = checkout.accessId!!,
                            accessPass = checkout.accessPass!!,
                        )
                    )
                    .getOrElse { throw it }

                val transaction =
                    createTransactionByCheckout
                        .execute(CreateTransactionByCheckout.Input(checkout))
                        .getOrElse { throw it }
                        .transaction
                postCreditCard3DSecureProcessor.execute(
                    PostCreditCard3DSecureProcessor.Input(checkout.id!!, transaction.id!!)
                )

                return HandleTds2transResultOutput(true)
            }

            CHALLENGE -> {
                return HandleTds2transResultOutput(true, challengeUrl)
            }

            UNCERTIFIED,
            VERIFICATION_FAILED,
            REJECT -> {
                return HandleTds2transResultOutput(false)
            }
        }
    }

    private fun handleTds2transSingleOrderResult(
        gmoTds2AuthResult: GmoTds2TransResult,
        checkout: Checkout,
        challengeUrl: String?,
    ): HandleTds2transResultOutput {
        when (gmoTds2AuthResult) {
            SUCCESS,
            ATTEMPTED -> {
                secureTran2
                    .execute(
                        SecureTran2.Input(
                            accessId = checkout.accessId!!,
                            accessPass = checkout.accessPass!!,
                        )
                    )
                    .getOrElse { throw it }

                val transaction =
                    createTransactionByCheckout
                        .execute(CreateTransactionByCheckout.Input(checkout))
                        .getOrElse { throw it }
                        .transaction
                postCreditCard3DSecureSingleOrderProcessor.execute(
                    PostCreditCard3DSecureSingleOrderProcessor.Input(
                        checkout.id!!,
                        transaction.id!!,
                    )
                )

                return HandleTds2transResultOutput(true)
            }

            CHALLENGE -> {
                return HandleTds2transResultOutput(true, challengeUrl)
            }

            UNCERTIFIED,
            VERIFICATION_FAILED,
            REJECT -> {
                return HandleTds2transResultOutput(false)
            }
        }
    }
}
